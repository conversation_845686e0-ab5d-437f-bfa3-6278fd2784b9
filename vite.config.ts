/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2024-12-22 18:07:10
 * @Description:
 */
import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";
import vuetify from "vite-plugin-vuetify";
import legacy from "@vitejs/plugin-legacy";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    vuetify(),
    legacy({
      targets: "> 0.25%, last 2 versions, ie > 8",
      modernPolyfills: true
    })
  ],
  base: "/static/design-workshop/",
  build: {
    target: "es2015"
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url))
    }
  }
});
