/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-28 14:47:10
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-28 14:47:21
 * @Description:
 */
import { Group, FabricObject } from "fabric";

export default class CustomGroup extends Group {
  add(...objects: FabricObject[]) {
    const result = super.add(...objects);

    const existingObjs = this.getObjects();
    for (const obj of objects) {
      let i = 0;
      for (const existingObj of existingObjs) {
        if (obj === existingObj) {
          break;
        }

        if (obj.get("depth") < existingObj.get("depth")) {
          super.moveObjectTo(obj, i);
          break;
        }

        i++;
      }
    }
    return result;
  }
}
