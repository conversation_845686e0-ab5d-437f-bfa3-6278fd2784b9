/*
 * @Author: ha<PERSON><PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-11-25 10:44:29
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-28 10:37:44
 * @Description:
 */
import {
  Canvas,
  FabricObject,
  type FabricObjectProps,
  type SerializedObjectProps,
  type ObjectEvents
} from "fabric";

export default class CustomFabricCanvas extends Canvas {
  add(
    ...objects: FabricObject<
      Partial<FabricObjectProps>,
      SerializedObjectProps,
      ObjectEvents
    >[]
  ): number {
    const result = super.add(...objects);

    const existingObjs = this.getObjects();
    for (const obj of objects) {
      let i = 0;
      for (const existingObj of existingObjs) {
        if (obj === existingObj) {
          break;
        }

        if (obj.get("depth") < existingObj.get("depth")) {
          super.moveObjectTo(obj, i);
          break;
        }

        i++;
      }
    }
    return result;
  }
  moveObjectTo(object: FabricObject, index: number): boolean {
    const existingObjs = this.getObjects();
    let i = 0;
    for (const existingObj of existingObjs) {
      if (object === existingObj) {
        continue;
      }

      const depth = existingObj.get("depth");

      if (index < depth) {
        return super.moveObjectTo(object, i);
      }

      i++;
    }

    return super.moveObjectTo(object, existingObjs.length - 1);
  }
}
