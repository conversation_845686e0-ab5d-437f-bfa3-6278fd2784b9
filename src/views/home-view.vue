<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-06-23 17:59:28
 * @Description:
-->
<script setup lang="ts">
import { ref } from "vue";
import { useBookStore } from "@/stores/book";
import { BOOK_PAGE_SIZE } from "@/constants";

const bookStore = useBookStore();

const current = ref("16-9");
const setCurrent = (index: string) => {
  current.value = index;
};

const setBookSize = () => {
  const [width, height] =
    BOOK_PAGE_SIZE[current.value as keyof typeof BOOK_PAGE_SIZE];
  bookStore.setPageSize(width, height);
};
</script>

<template>
  <div class="home-view">
    <div class="home-view__header">
      <div class="home-view__logo"></div>
      <div class="home-view__title">
        <img
          class="home-view__title-img"
          src="@/assets/images/home-title.png"
        />
      </div>
    </div>
    <div class="home-view__main">
      <div class="home-view__content">
        <div
          class="home-view__content-item"
          :class="{ 'home-view__content-item--active': current === '16-9' }"
          @click="setCurrent('16-9')"
        >
          <div class="home-view__content-item-border">
            <img
              class="home-view__content-item-img"
              src="@/assets/images/example-16-9.png"
            />
          </div>
          <div class="home-view__content-item-title">
            <span class="home-view__content-item-title-text">16 : 9</span>
          </div>
        </div>
        <div
          class="home-view__content-item"
          :class="{ 'home-view__content-item--active': current === '1-1' }"
          @click="setCurrent('1-1')"
        >
          <div class="home-view__content-item-border">
            <img
              class="home-view__content-item-img"
              src="@/assets/images/example-1-1.png"
            />
          </div>
          <div class="home-view__content-item-title">
            <span class="home-view__content-item-title-text">1 : 1</span>
          </div>
        </div>
        <div
          class="home-view__content-item"
          :class="{ 'home-view__content-item--active': current === '9-16' }"
          @click="setCurrent('9-16')"
        >
          <div class="home-view__content-item-border">
            <img
              class="home-view__content-item-img"
              src="@/assets/images/example-9-16.png"
            />
          </div>
          <div class="home-view__content-item-title">
            <span class="home-view__content-item-title-text">9 : 16</span>
          </div>
        </div>
      </div>
      <div class="home-view__comfirm">
        <v-btn class="home-view__comfirm-btn" @click="setBookSize" to="/edit"
          >开始创作</v-btn
        >
      </div>
    </div>
  </div>
</template>

<style>
.home-view {
  overflow: auto;
  height: 100%;
  background: linear-gradient(
    #222223 10%,
    rgb(var(--v-theme-background)) 10%,
    rgb(var(--v-theme-background)) 50%,
    #222223 50%,
    #222223 60%,
    rgb(var(--v-theme-background)) 60%,
    rgb(var(--v-theme-background)) 100%
  );
  background-position: 0 0;
  background-size: 20px 20px;
}

.home-view__logo {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  padding-top: 17.536%;
  width: 30%;
  min-width: 160px;
  max-width: 210px;
  background: url("@/assets/images/logo.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.home-view__title {
  padding-top: 5.375rem;
  text-align: center;
}

.home-view__title-img {
  width: 26.77%;
  min-width: 16.875rem;
  max-width: 30rem;
}

.home-view__main {
  padding-right: 4.125rem;
  padding-left: 4.125rem;
}

@media screen and (width <= 768px) {
  .home-view__main {
    padding-right: 1.25rem;
    padding-left: 1.25rem;
  }
}

.home-view__content {
  display: flex;
  padding-top: 2.5rem;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.home-view__content-item {
  margin: 0 1%;
  width: 31%;
  max-width: 30rem;
  cursor: pointer;
}

@media screen and (width <= 768px) {
  .home-view__content-item {
    width: 55%;
    min-width: 15rem;
  }
}

.home-view__content-item-border {
  position: relative;
  border: 1px solid #949494;
  border-radius: 30px;
  padding-top: 85.185%;
  background-image: linear-gradient(180deg, #1d1d1e 7%, #232324 100%);
  box-shadow: 6px 6px 26px 0 rgb(0 0 0 / 70%);
}

.home-view__content-item--active .home-view__content-item-border {
  border: 2px solid #0082ff;
  background: #0f0f11;
}

.home-view__content-item-img {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 95%;
  height: auto;
  transform: translate(-50%, -50%);
}

.home-view__content-item-title {
  margin: 1rem 0;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
}

.home-view__content-item--active .home-view__content-item-title-text {
  border-radius: 17px;
  padding: 0 0.7rem;
  background: #0179ff;
}

.home-view__comfirm {
  padding: 4rem 0;
  text-align: center;
}

@media screen and (width <= 768px) {
  .home-view__comfirm {
    padding: 2rem 0;
  }
}

.home-view .home-view__comfirm .home-view__comfirm-btn {
  border-radius: 50px;
  width: 50%;
  min-width: 15rem;
  max-width: 35rem;
  height: 3rem;
  min-height: 2rem;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
  background-image: linear-gradient(180deg, #007aff 6%, #0068ff 100%);
  box-shadow:
    0 3px 10px 0 rgb(0 0 0 / 90%),
    inset 0 2px 0 0 #87b6ff;
}
</style>
