<template>
  <div class="wechat-notice-view">
    <div class="wechat-notice-view__content">
      <p>为了良好的使用体验，请使用浏览器打开</p>
      <p>1. 点击右上角 [...]</p>
      <p>2. 点击 [在默认浏览器中打开]</p>
      <img
        class="wechat-notice-view__deer"
        src="@/assets/images/deer.png"
        alt="deer"
      />
    </div>
  </div>
</template>

<style>
.wechat-notice-view {
  overflow: auto;
  height: 100%;
  background: linear-gradient(
    #222223 10%,
    rgb(var(--v-theme-background)) 10%,
    rgb(var(--v-theme-background)) 50%,
    #222223 50%,
    #222223 60%,
    rgb(var(--v-theme-background)) 60%,
    rgb(var(--v-theme-background)) 100%
  );
  background-position: 0 0;
  background-size: 20px 20px;
}

.wechat-notice-view__content {
  padding-top: 2rem;
  font-size: 1.3rem;
  line-height: 2rem;
  text-align: center;
}

@media (width <= 375px) {
  .wechat-notice-view__content {
    font-size: 1rem;
  }
}

.wechat-notice-view__deer {
  margin-top: 5rem;
}
</style>
