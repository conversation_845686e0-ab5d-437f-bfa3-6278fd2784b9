<!--
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-21 10:12:58
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2025-01-21 11:06:13
 * @Description:
-->
<script setup lang="ts">
import TEMPLATE_0_PNG from "@/assets/images/templates/template-0.jpeg";
import TEMPLATE_1_PNG from "@/assets/images/templates/template-1.jpeg";
import TEMPLATE_2_PNG from "@/assets/images/templates/template-2.jpeg";
import TEMPLATE_3_PNG from "@/assets/images/templates/template-3.jpeg";
import TEMPLATE_4_PNG from "@/assets/images/templates/template-4.jpeg";
import TEMPLATE_5_PNG from "@/assets/images/templates/template-5.jpeg";
import TEMPLATE_6_PNG from "@/assets/images/templates/template-6.jpeg";
import TEMPLATE_7_PNG from "@/assets/images/templates/template-7.jpeg";
import TEMPLATE_8_PNG from "@/assets/images/templates/template-8.jpeg";

const templates = [
  TEMPLATE_0_PNG,
  TEMPLATE_1_PNG,
  TEMPLATE_2_PNG,
  TEMPLATE_3_PNG,
  TEMPLATE_4_PNG,
  TEMPLATE_5_PNG,
  TEMPLATE_6_PNG,
  TEMPLATE_7_PNG,
  TEMPLATE_8_PNG
];
</script>

<template>
  <div>
    <div v-for="(item, index) in templates" :key="index">
      <div class="template-view__img-box">
        <img class="template-view__img" :src="item" alt="template" />
        <a
          class="template-view__download-btn"
          :href="item"
          download="template.jpeg"
          >下载</a
        >
      </div>
    </div>
  </div>
</template>

<style>
.template-view__img-box {
  position: relative;
  margin: 0 auto;
  margin-top: 10px;
  width: 80%;
  min-width: 300px;
  max-width: 1140px;
}

.template-view__img {
  border-radius: 10px;
  width: 100%;
}

.template-view__download-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  border: 2px solid #9ccaff;
  border-radius: 10px;
  padding: 10px 20px;
  font-weight: 800;
  text-decoration: none;
  color: white;
  background-color: #2e3daf;
  cursor: pointer;
  user-select: none;
}
</style>
