import { useBookStore } from "@/stores/book";
import { useTemplateStore } from "@/stores/template";
import type { TemplateConfig } from "@/template";
const handleTemplate = async () => {
  const templateStore = useTemplateStore();
  if (!templateStore.isTemplateSet) {
    return;
  }

  const bookStore = useBookStore();

  const pages = (templateStore.template as TemplateConfig).pages;
  for (let i = 0; i < pages.length; i++) {
    const page = pages[i];
    bookStore.addPageWithElements(page);
  }
};

export default handleTemplate;
