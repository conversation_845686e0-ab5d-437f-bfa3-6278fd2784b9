<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-29 11:17:04
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-06-23 18:58:09
 * @Description:
-->
<script setup lang="ts">
import { ref } from "vue";
import HeaderBar from "@/components/header-bar.vue";
import LeftNav from "@/components/left-nav.vue";
import CenterMain from "@/components/center-main.vue";
import RightToolBar from "@/components/right-tool-bar.vue";
import { onMounted, onUnmounted } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import { getExitingMessage } from "@/utils/get-message";
import { usePageStore } from "@/stores/web-page";
import { useBookStore } from "@/stores/book";
import TipsOverlay from "@/components/tips-overlay.vue";
import handleTemplate from "./helpers/handle-template";

const bookStore = useBookStore();
const pageStore = usePageStore();

import TEMPLATE_CONFIG from "@/template";
import { useTemplateStore } from "@/stores/template";

const templateStore = useTemplateStore();
templateStore.setTemplate(TEMPLATE_CONFIG[0]);

const isPortrait = ref(window.innerHeight > window.innerWidth);
const closeTips = () => {
  isPortrait.value = false;
};

// 意外离开页面是进行提示
const onExit = (event: BeforeUnloadEvent) => {
  if (!pageStore.hasModified) {
    return;
  }
  const message = getExitingMessage();
  event.returnValue = message; // Chrome 和 Firefox
  return message; // 其他浏览器的兼容性
};

onBeforeRouteLeave((to, from, next) => {
  if (!pageStore.hasModified) {
    next();
    return;
  }
  const result = window.confirm(getExitingMessage());
  if (result) {
    next();
  } else {
    next(false);
  }
});

onMounted(() => {
  window.addEventListener("beforeunload", onExit);
  handleTemplate();
});

onUnmounted(() => {
  window.removeEventListener("beforeunload", onExit);
  pageStore.$reset();
  bookStore.$reset();
});
</script>

<template>
  <v-app class="edit-view">
    <tips-overlay></tips-overlay>
    <header-bar></header-bar>
    <left-nav></left-nav>
    <center-main></center-main>
    <right-tool-bar></right-tool-bar>
    <div class="edit-view__tips-landscape" v-if="isPortrait">
      <div class="edit-view__tips-landscape-overlay"></div>
      <div class="edit-view__tips-landscape-box">
        <p>横屏体验更佳哦～</p>
        <button
          class="edit-view__tips-landscape-confirm-btn"
          @click="closeTips"
        >
          确定
        </button>
      </div>
    </div>
  </v-app>
</template>

<style>
.edit-view {
  height: 100%;
}

.edit-view__tips-landscape {
  position: fixed;
  inset: 0;
  z-index: 3000;
  pointer-events: all;
}

.edit-view__tips-landscape-overlay {
  position: absolute;
  background-color: #000;
  opacity: 0.8;
  inset: 0;
}

.edit-view__tips-landscape-box {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 1px solid #fff;
  border-radius: 10px;
  padding: 3.75rem 1rem 2.75rem;
  width: 80%;
  max-width: 25rem;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: #fff;
  background-color: #000;
  transform: translate(-50%, -50%);
}

.edit-view__tips-landscape-confirm-btn {
  margin-top: 30px;
  border-radius: 31px;
  width: 100%;
  min-width: 1rem;
  max-width: 13rem;
  height: 40px;
  background-image: linear-gradient(180deg, #007aff 6%, #0068ff 100%);
  box-shadow:
    inset 0 1px 0 0 #87b6ff,
    0 0 6px 0 rgb(0 0 0 / 90%),
    0 0 6px 0 rgb(0 0 0 / 90%);
}
</style>
