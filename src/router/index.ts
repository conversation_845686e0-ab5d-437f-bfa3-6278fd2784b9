/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-23 18:18:51
 * @Description:
 */
import { createRouter, createWebHistory } from "vue-router";
import type { RouteComponent } from "vue-router";
import checkWechatClient from "@/utils/check-wechat-client";

/** 判断是否是微信客户端 */
const IS_WECHAT_CLIENT = checkWechatClient();
/**
 * 判断是否是微信客户端，如果是，则返回微信通知组件，否则返回传入的组件
 * @param component 异步组件
 * @returns 异步组件或微信通知组件
 */
const shouldWechatNoticeComponentOrComponent = (
  component: () => Promise<RouteComponent>
) => {
  return IS_WECHAT_CLIENT
    ? () => import("@/views/wechat-notice-view.vue")
    : component;
};

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: shouldWechatNoticeComponentOrComponent(
        () => import("@/views/home-view.vue")
      )
    },

    {
      path: "/edit",
      name: "edit",
      component: shouldWechatNoticeComponentOrComponent(
        () => import("@/views/edit-view")
      )
    },

    {
      path: "/template",
      name: "template",
      component: shouldWechatNoticeComponentOrComponent(
        () => import("@/views/template-view.vue")
      )
    }
  ]
});

export default router;
