/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-23 13:37:18
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-01-20 13:35:05
 * @Description:
 */
import FONT_FAMILY_FENGYASONG from "@/assets/fonts/fengyasong.css?url";
import FONT_FAMILY_GANLANJIANTI from "@/assets/fonts/ganlanjianti.css?url";
import FONT_FAMILY_KANGKANGTI from "@/assets/fonts/kangkangti.css?url";
import FONT_FAMILY_MEIHEITI from "@/assets/fonts/meiheiti.css?url";
import FONT_FAMILY_MOREN from "@/assets/fonts/moren.css?url";
import FONT_FAMILY_PANGCUTI from "@/assets/fonts/pangcuti.css?url";
import FONT_FAMILY_XIANGSUTI from "@/assets/fonts/xiangsuti.css?url";
import FONT_FAMILY_XIEHEITI from "@/assets/fonts/xieheiti.css?url";
import FONT_FAMILY_XINYENIANTI from "@/assets/fonts/xinyenianti.css?url";

import GRAPHICS_CIRCLE from "@/assets/images/graphics/graphics/circle.png";
import GRAPHICS_CLOUD from "@/assets/images/graphics/graphics/cloud.png";
import GRAPHICS_FOUR_CLOUD from "@/assets/images/graphics/graphics/four-cloud.png";
import GRAPHICS_PAPER from "@/assets/images/graphics/graphics/paper.png";
import GRAPHICS_RECT_VERTICAL from "@/assets/images/graphics/graphics/rect-vertical.png";
import GRAPHICS_RECT from "@/assets/images/graphics/graphics/rect.png";
import GRAPHICS_SECTOR from "@/assets/images/graphics/graphics/sector.png";
import GRAPHICS_TWELVE_CLOUD from "@/assets/images/graphics/graphics/twelve_cloud.png";

import GRAPHICS_THUMBNAILS_CIRCLE from "@/assets/images/graphics/thumbnails/circle.png";
import GRAPHICS_THUMBNAILS_CLOUD from "@/assets/images/graphics/thumbnails/cloud.png";
import GRAPHICS_THUMBNAILS_FOUR_CLOUD from "@/assets/images/graphics/thumbnails/four-cloud.png";
import GRAPHICS_THUMBNAILS_PAPER from "@/assets/images/graphics/thumbnails/paper.png";
import GRAPHICS_THUMBNAILS_RECT_VERTICAL from "@/assets/images/graphics/thumbnails/rect-vertical.png";
import GRAPHICS_THUMBNAILS_RECT from "@/assets/images/graphics/thumbnails/rect.png";
import GRAPHICS_THUMBNAILS_SECTOR from "@/assets/images/graphics/thumbnails/sector.png";
import GRAPHICS_THUMBNAILS_TWELVE_CLOUD from "@/assets/images/graphics/thumbnails/twelve_cloud.png";

export const THUMBNAIL_WIDTH = 120;

export const BOOK_PAGE_MAX_QUANTITY = 20;

export const BOOK_PAGE_SIZE = {
  "16-9": [1140, 641],
  "1-1": [641, 641],
  "9-16": [641, 1140]
};

export const BOOK_PAGE_DEFAULT_SIZE = BOOK_PAGE_SIZE["16-9"];

export const CANVAS_BG_COLOR = "#232325";
export const CANVAS_INPUT_PLACEHOLDER = "输入文本";
export const CANVAS_MAX_SCALE = 2;
export const CANVAS_MIN_SCALE = 0.1;
export const CANVAS_SCALE_STEP = 0.1;
export const CANVAS_DEFAULT_SCALE = 1;

// 剪切框的颜色
export const CLIP_BOX_COLOR = "#0082FF";
export const CLIP_BOX_DASH = [5, 5];
export const CLIP_BOX_MASKER_OPACITY = 0.3;

export const DEG_TO_RAD = Math.PI / 180;

export const IMG_SCALE_MIN_VALUE = 0.1;
export const IMG_SCALE_MAX_VALUE = 3;

export const IMG_OPACITY_MIN_VALUE = 0;
export const IMG_OPACITY_MAX_VALUE = 1;

export const TEXT_MAX_QUANTITY = 8;
export const TEXT_COLOR_RANGE = [
  "#ffffff",
  "#FFC175",
  "#FF9400",
  "#EE230D",
  "#962900",
  "#0082FF",
  "#027C77",
  "#53366E",
  "#1D3762",
  "#000000"
];
export const TEXT_DEFAULT_COLOR = TEXT_COLOR_RANGE[0];

export const TEXT_INIT_WIDTH = 260;
export const TEXT_MIN_FONTSIZE = 10;
export const TEXT_MAX_FONTSIZE = 280;
export const TEXT_DEFAULT_FONTSIZE = 20;

export const TEXT_MIN_OPACITY = 0;
export const TEXT_MAX_OPACITY = 1;
export const TEXT_DEFAULT_OPACITY = TEXT_MAX_OPACITY;

export const TEXT_CHAR_SPACING = 100;

export const TEXT_FONT_FAMILY = {
  moren: {
    name: "默认",
    value: "",
    url: FONT_FAMILY_MOREN
  },
  xieheiti: {
    name: "斜黑体",
    value: "",
    url: FONT_FAMILY_XIEHEITI
  },
  fengyasong: {
    name: "风雅宋",
    value: "",
    url: FONT_FAMILY_FENGYASONG
  },
  meiheiti: {
    name: "美黑体",
    value: "",
    url: FONT_FAMILY_MEIHEITI
  },
  kangkangti: {
    name: "康康体",
    value: "",
    url: FONT_FAMILY_KANGKANGTI
  },
  xinyenianti: {
    name: "新叶念体",
    value: "",
    url: FONT_FAMILY_XINYENIANTI
  },
  xiangsuti: {
    name: "像素体",
    value: "",
    url: FONT_FAMILY_XIANGSUTI
  },
  pangcuti: {
    name: "胖粗体",
    value: "",
    url: FONT_FAMILY_PANGCUTI
  },
  ganlanjianti: {
    name: "橄榄简体",
    value: "",
    url: FONT_FAMILY_GANLANJIANTI
  }
};

export const TEXT_DEFAULT_FONT_FAMILY = "moren";

export const TEXT_FONT_FAMILY_LOAD_TIMEOUT = 15000;

export const GRAPHICS = [
  {
    thumbnail: GRAPHICS_THUMBNAILS_CLOUD,
    url: GRAPHICS_CLOUD,
    opacity: 0.7
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_SECTOR,
    url: GRAPHICS_SECTOR,
    opacity: 1
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_FOUR_CLOUD,
    url: GRAPHICS_FOUR_CLOUD,
    opacity: 1
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_RECT_VERTICAL,
    url: GRAPHICS_RECT_VERTICAL,
    opacity: 0.7
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_CIRCLE,
    url: GRAPHICS_CIRCLE,
    opacity: 0.7
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_RECT,
    url: GRAPHICS_RECT,
    opacity: 0.7
  },

  {
    thumbnail: GRAPHICS_THUMBNAILS_PAPER,
    url: GRAPHICS_PAPER,
    opacity: 1
  },
  {
    thumbnail: GRAPHICS_THUMBNAILS_TWELVE_CLOUD,
    url: GRAPHICS_TWELVE_CLOUD,
    opacity: 0.7
  }
];
export const GRAPHICS_MAX_QUANTITY = 8;

export const IMAGE_MAX_QUANTITY = 8;

export const UPLOAD_IMG_MAX_SIZE = 1024 * 1024 * 50; // 50M

export const MESSAGE_WHEN_EXITING_PAGE_AFTER_EXPORT =
  "退出后，绘本已创作内容将完全消失";
export const MESSAGE_WHEN_EXITING_PAGE_BEFORE_EXPORT =
  "绘本已创作内容还未导出，退出后内容将消失";
