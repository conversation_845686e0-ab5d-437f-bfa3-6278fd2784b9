/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-23 16:23:47
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-06-25 17:06:10
 * @Description:]
 */
import T1_BG_PNG from "./assets/images/template-1/bg.png";
import T1_THUMBNAIL_PNG from "./assets/images/template-1/thumbnail.png";
import type { IImgData, IImgUploadBtn, ITextboxData } from "@/types/book";
import { TEXT_FONT_FAMILY } from "@/constants";

interface TemplatePageConfig {
  thumbnail: string;
  bg: Partial<IImgData>;
  texts?: Partial<
    Omit<ITextboxData, "fontFamily"> & {
      fontFamily: keyof typeof TEXT_FONT_FAMILY;
    }
  >[];
  imgUploadBtns?: Partial<IImgUploadBtn>[];
  pdfs?: Partial<IImgData>[];
}

interface TemplateConfig {
  name: string;
  pages: TemplatePageConfig[];
}

const CONFIG: TemplateConfig[] = [
  {
    name: "模板1",
    pages: [
      {
        thumbnail: T1_THUMBNAIL_PNG,
        bg: {
          src: T1_BG_PNG
        },
        texts: [
          {
            text: "请输入文本",
            left: 0,
            top: 0,
            fontSize: 120,
            fontFamily: "xiangsuti",
            color: "#ff00ff",
            width: 650
          }
        ],
        imgUploadBtns: [
          {
            left: 300,
            top: 300,
            scaleX: 3.5,
            scaleY: 3.5
          },
          {
            left: 600,
            top: 300,
            scaleX: 3,
            scaleY: 3
          }
        ]
      }
    ]
  }
] as const;

export default CONFIG;

export type { TemplateConfig };
