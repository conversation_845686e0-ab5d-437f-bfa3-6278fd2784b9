/*
 * @Author: ha<PERSON><PERSON><PERSON> han<PERSON>@zuoyebang.com
 * @Date: 2024-11-15 18:29:42
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-09 14:22:05
 * @Description:
 */
import type { ThemeDefinition } from "vuetify";
const theme: ThemeDefinition = {
  dark: true,
  colors: {
    background: "#181819",
    surface: "#2B2D30",
    primary: "#0068FF",
    "on-primary": "#ffffff",
    "on-surface": "#A3AAB5",
    "on-surface-text-hover": "#B3BBC8",
    "on-surface-text-active": "#DCE0E5",
    "on-surface-btn-hover": "#383d41",
    "on-surface-btn-active": "#4e5257",
    "surface-darken-1": "#181819",
    "on-surface-darken-1-border-color": "#7c7e82",
    "surface-darken-2": "#232325",
    "on-surface-darken-2": "#bcb5b3",
    "surface-darken-3": "#1e1e1e",
    "on-surface-darken-3": "#E9E9E9",
    "surface-darken-4": "#000000",
    "on-surface-darken-4": "#ffffff",
    "surface-lighten-1": "#2E2E2E",
    "on-surface-lighten-1": "#ffffff",
    "on-surface-lighten-1-btn-bg": "#393939",
    "on-surface-lighten-1-btn-border": "#494949",
    "on-surface-lighten-1-btn-shadow": "#646464",
    "on-surface-lighten-1-btn-shadow-inset": "#353535",
    "on-surface-lighten-1-btn-shadow-inset-1": "#6099EB",
    "on-surface-lighten-1-btn-active-shadow": "#2d2d2d",
    "on-surface-lighten-1-btn-active-shadow-1": "#1a1a1a",
    "on-surface-lighten-1-btn-active-shadow-inset": "#959595",
    "on-surface-lighten-1-input": "#3b3b3b",
    "on-surface-lighten-1-input-border": "#535353",
    "on-surface-lighten-1-input-border-shadow": "#313131",
    "on-surface-lighten-1-divider": "#3b3b3b",
    "on-surface-lighten-1-arrow": "#0379EB",
    "on-surface-lighten-1-active": "#0082FF",
    "on-surface-lighten-1-active-2": "#6B6B6B",
    "on-surface-lighten-1-hover": "#5e9eff"
  },
  variables: {
    "border-color": "#899099"
  }
};

export default theme;
