import type {
  IBook,
  IBook<PERSON><PERSON>,
  IImgData,
  IImgUploadBtn,
  ITextboxData
} from "@/types/book";
import { ElementType, TextDirection } from "@/types/book";
import { defineStore } from "pinia";
import { getUUID } from "@/utils/get-uuid";
import {
  BOOK_PAGE_DEFAULT_SIZE,
  BOOK_PAGE_MAX_QUANTITY,
  TEXT_MAX_QUANTITY,
  CANVAS_INPUT_PLACEHOLDER,
  TEXT_DEFAULT_COLOR,
  TEXT_DEFAULT_FONTSIZE,
  GRAPHICS_MAX_QUANTITY,
  TEXT_DEFAULT_FONT_FAMILY,
  TEXT_CHAR_SPACING,
  TEXT_INIT_WIDTH,
  IMAGE_MAX_QUANTITY
} from "@/constants/index";
import { AddElementResult, RemoveElementResult } from "@/types/book";

export const useBookStore = defineStore("book", {
  state: (): IBook => {
    return {
      pages: [
        {
          ID: 0,
          thumbnail: undefined,
          bg: undefined,
          texts: {},
          graphics: {},
          images: {},
          imgUploadBtns: {},
          selected: undefined,
          clipped: undefined
        }
      ],
      prePages: [],
      currentPageIndexInArray: 0,
      currentPageID: 0,
      lastPageID: 0,
      width: BOOK_PAGE_DEFAULT_SIZE[0],
      height: BOOK_PAGE_DEFAULT_SIZE[1]
    };
  },
  getters: {
    isEmpty(state): boolean {
      const i = state.pages.findIndex((item) => {
        return state.currentPageID === item.ID;
      });
      if (i === -1) {
        return true;
      }

      const page = state.pages[i];
      return (
        page.bg === undefined &&
        Object.keys(page.texts).length === 0 &&
        Object.keys(page.graphics).length === 0 &&
        Object.keys(page.images).length === 0
      );
    }
  },

  actions: {
    _getNewPageID() {
      return ++this.lastPageID;
    },
    setPageSize(width: number, height: number) {
      this.width = width;
      this.height = height;
    },
    addPage() {
      if (this.pages.length >= BOOK_PAGE_MAX_QUANTITY) {
        return AddElementResult.OVER_MAX_QUANTITY;
      }

      this.pages.push({
        ID: this._getNewPageID(),
        thumbnail: undefined,
        bg: undefined,
        texts: {},
        graphics: {},
        images: {},
        imgUploadBtns: {},
        selected: undefined,
        clipped: undefined
      });
      return AddElementResult.OK;
    },
    addPageByConfig(page: IBookPage) {
      if (this.pages.length >= BOOK_PAGE_MAX_QUANTITY) {
        return;
      }

      this.pages.push(page);
    },
    addPageWithElements(page: {
      thumbnail: string;
      bg?: Partial<IImgData>;
      texts?: Partial<ITextboxData>[];
      imgUploadBtns?: Partial<IImgUploadBtn>[];
    }) {
      const result = this.addPage();
      if (result !== AddElementResult.OK) {
        return;
      }

      const pageID = this.lastPageID;

      const { thumbnail, bg, texts, imgUploadBtns } = page;

      if (thumbnail) {
        this.setPageThumbnail(pageID, thumbnail);
      }
      if (bg) {
        this.addPageBG(pageID, bg);
      }

      if (texts && texts.length > 0) {
        texts.forEach((text) => {
          this.addPageText(pageID, text);
        });
      }

      if (imgUploadBtns && imgUploadBtns.length > 0) {
        imgUploadBtns.forEach((image) => {
          this.addPageImgUploadBtn(pageID, image);
        });
      }
    },
    removePage(pageID: number) {
      if (this.pages.length <= 1) {
        return RemoveElementResult.OVER_MIN_QUANTITY;
      }

      const index = this.getPageIndexInArray(pageID);
      if (index === -1) {
        return RemoveElementResult.PAGE_NOT_FOUND;
      }

      if (pageID === this.currentPageID) {
        if (index >= this.pages.length - 1) {
          this.setCurrentPage(this.pages[this.pages.length - 2].ID);
        } else {
          this.setCurrentPage(this.pages[index + 1].ID);
        }
      }

      this.pages.splice(index, 1);
      this.updateCurrentPageIndexInArray();
      return RemoveElementResult.OK;
    },
    getPage(pageID: number) {
      const i = this.getPageIndexInArray(pageID);
      if (i === -1) {
        return;
      }

      return this.pages[i];
    },
    setCurrentPage(pageID: number) {
      this.currentPageID = pageID;
      // 之所以不用getter，是因为watch 监听器的执行顺序问题
      this.updateCurrentPageIndexInArray();
    },
    updateCurrentPageIndexInArray() {
      this.currentPageIndexInArray = this.getPageIndexInArray(
        this.currentPageID
      );
    },
    getPageIndexInArray(pageID: number): number {
      return this.pages.findIndex((item) => {
        return pageID === item.ID;
      });
    },
    addPrePageWithBG(src: string) {
      if (this.pages.length >= BOOK_PAGE_MAX_QUANTITY) {
        return;
      }

      const pageID = this._getNewPageID();
      this.prePages.push({
        ID: pageID,
        thumbnail: undefined,
        bg: {
          ID: getUUID(),
          type: ElementType.BG,
          left: 0,
          top: 0,
          width: 0,
          height: 0,
          scaleX: 1,
          scaleY: 1,
          shouldScaleToFitContainer: true,
          src: src,
          angle: 0,
          opacity: 1,
          depth: 0,
          objHasModified: false,
          belongToPage: pageID
        },
        texts: {},
        graphics: {},
        images: {},
        imgUploadBtns: {},
        selected: undefined,
        clipped: undefined
      });
    },
    removeAllPrePage() {
      this.prePages.length = 0;
    },
    checkElementExist(pageID: number, elementID: string) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (elementID === page.bg?.ID) {
        return true;
      }
      if (elementID in page.texts) {
        return true;
      }
      if (elementID in page.graphics) {
        return true;
      }
      if (elementID in page.images) {
        return true;
      }

      return false;
    },
    getElementShouldDepth(pageID: number) {
      const page = this.getPage(pageID);
      if (!page) {
        return 0;
      }

      return (
        Object.keys(page.texts).length +
        Object.keys(page.graphics).length +
        Object.keys(page.images).length +
        1
      );
    },
    updateElement(
      PageID: number,
      elementID: string,
      config: Partial<IImgData | ITextboxData>,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(PageID);
      if (!page) {
        return;
      }

      if (elementID === page.bg?.ID) {
        this.updatePageBG(PageID, config, objHasModified);
      } else if (elementID in page.texts) {
        this.updatePageText(PageID, elementID, config, objHasModified);
      } else if (elementID in page.graphics) {
        this.updatePageGraphics(PageID, elementID, config, objHasModified);
      } else if (elementID in page.images) {
        this.updatePageImage(PageID, elementID, config, objHasModified);
      }
    },

    removeElement(pageID: number, elementID: string) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (elementID === page.bg?.ID) {
        this.removePageBg(pageID);
      } else if (elementID in page.texts) {
        this.removePageText(pageID, elementID);
      } else if (elementID in page.graphics) {
        this.removePageGraphics(pageID, elementID);
      } else if (elementID in page.images) {
        this.removePageImage(pageID, elementID);
      }
    },
    addPageBG(
      pageID: number,
      config: Partial<IImgData> | undefined,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return AddElementResult.PAGE_NOT_FOUND;
      }

      if (!config || !config.src) {
        return AddElementResult.NO_SRC;
      }

      if (page.bg) {
        return AddElementResult.EXISTED;
      }
      const bg = {
        ID: getUUID(),
        type: ElementType.BG,
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        scaleX: 1,
        scaleY: 1,
        shouldScaleToFitContainer: true,
        src: "",
        angle: 0,
        opacity: 1,
        depth: 0,
        objHasModified: objHasModified,
        belongToPage: pageID
      };

      if (config) {
        const keys = Object.keys(bg) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (bg[key] as string | number | boolean) = config[key];
        }
      }

      page.bg = bg;
      return AddElementResult.OK;
    },
    updatePageBG(
      pageID: number,
      config: Partial<IImgData> | undefined,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page || !page.bg) {
        return;
      }

      page.bg.objHasModified = objHasModified;

      if (config) {
        const keys = Object.keys(page.bg) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (page.bg[key] as string | number | boolean) = config[key];
        }
      }
    },
    removePageBg(pageID: number) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      page.bg = undefined;
    },
    addPageText(
      pageID: number,
      config?: Partial<ITextboxData>,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return AddElementResult.PAGE_NOT_FOUND;
      }

      if (Object.keys(page.texts).length >= TEXT_MAX_QUANTITY) {
        return AddElementResult.OVER_MAX_QUANTITY;
      }

      const textID = getUUID();
      const text: ITextboxData = {
        ID: textID,
        type: ElementType.TEXTBOX,
        left: 0,
        top: 0,
        width: TEXT_INIT_WIDTH,
        height: 0,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        opacity: 1,
        text: CANVAS_INPUT_PLACEHOLDER,
        fontFamily: TEXT_DEFAULT_FONT_FAMILY,
        fontSize: TEXT_DEFAULT_FONTSIZE,
        charSpacing: TEXT_CHAR_SPACING,
        color: TEXT_DEFAULT_COLOR,
        direction: TextDirection.HORIZONTAL,
        depth: this.getElementShouldDepth(pageID) + 200,
        objHasModified: objHasModified,
        belongToPage: pageID
      };

      if (config) {
        const keys = Object.keys(text) as (keyof ITextboxData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (text[key] as string | number | boolean) = config[key];
        }
      }
      page.texts[textID] = text;
      return AddElementResult.OK;
    },
    updatePageText(
      pageID: number,
      textID: string,
      config: Partial<ITextboxData> | undefined,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (!(textID in page.texts)) {
        return;
      }

      page.texts[textID].objHasModified = objHasModified;

      if (config) {
        const keys = Object.keys(page.texts[textID]) as (keyof ITextboxData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (page.texts[textID][key] as string | number | boolean) = config[key];
        }
      }
    },
    removePageText(pageID: number, textID: string) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (textID in page.texts) {
        delete page.texts[textID];
      }
    },
    addPageGraphics(
      pageID: number,
      config?: Partial<IImgData>,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return AddElementResult.PAGE_NOT_FOUND;
      }

      if (Object.keys(page.graphics).length >= GRAPHICS_MAX_QUANTITY) {
        return AddElementResult.OVER_MAX_QUANTITY;
      }

      if (!config || !config.src) {
        return AddElementResult.NO_SRC;
      }

      const graphicsID = getUUID();
      const graphics = {
        ID: graphicsID,
        type: ElementType.GRAPHICS,
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        scaleX: 1,
        scaleY: 1,
        shouldScaleToFitContainer: true,
        src: "",
        angle: 0,
        opacity: 1,
        depth: this.getElementShouldDepth(pageID) + 100,
        objHasModified: objHasModified,
        belongToPage: pageID
      };

      if (config) {
        const keys = Object.keys(graphics) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (graphics[key] as string | number | boolean) = config[key];
        }
      }

      page.graphics[graphicsID] = graphics;
      return AddElementResult.OK;
    },
    updatePageGraphics(
      pageID: number,
      graphicsID: string,
      config: Partial<IImgData> | undefined,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (!(graphicsID in page.graphics)) {
        return;
      }

      page.graphics[graphicsID].objHasModified = objHasModified;

      if (config) {
        const keys = Object.keys(
          page.graphics[graphicsID]
        ) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (page.graphics[graphicsID][key] as string | number | boolean) =
            config[key];
        }
      }
    },
    removePageGraphics(pageID: number, graphicsID: string) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (graphicsID in page.graphics) {
        delete page.graphics[graphicsID];
      }
    },
    addPageImage(
      pageID: number,
      config?: Partial<IImgData>,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return AddElementResult.PAGE_NOT_FOUND;
      }

      if (Object.keys(page.images).length >= IMAGE_MAX_QUANTITY) {
        return AddElementResult.OVER_MAX_QUANTITY;
      }

      if (!config || !config.src) {
        return AddElementResult.NO_SRC;
      }

      const imageID = getUUID();
      const image = {
        ID: imageID,
        type: ElementType.IMAGE,
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        scaleX: 1,
        scaleY: 1,
        shouldScaleToFitContainer: true,
        src: "",
        angle: 0,
        opacity: 1,
        depth: this.getElementShouldDepth(pageID),
        objHasModified: objHasModified,
        belongToPage: pageID
      };

      if (config) {
        const keys = Object.keys(image) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (image[key] as string | number | boolean) = config[key];
        }
      }

      page.images[imageID] = image;
      return AddElementResult.OK;
    },
    updatePageImage(
      pageID: number,
      imageID: string,
      config: Partial<IImgData> | undefined,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (!(imageID in page.images)) {
        return;
      }

      page.images[imageID].objHasModified = objHasModified;

      if (config) {
        const keys = Object.keys(page.images[imageID]) as (keyof IImgData)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }

          (page.images[imageID][key] as string | number | boolean) =
            config[key];
        }
      }
    },
    removePageImage(pageID: number, imageID: string) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (imageID in page.images) {
        delete page.images[imageID];
      }
    },
    addPageImgUploadBtn(
      pageID: number,
      config?: Partial<IImgUploadBtn>,
      objHasModified: boolean = false
    ) {
      const page = this.getPage(pageID);
      if (!page) {
        return AddElementResult.PAGE_NOT_FOUND;
      }

      const imgUploadBtnID = getUUID();
      const imgUploadBtn: IImgUploadBtn = {
        ID: imgUploadBtnID,
        type: ElementType.IMG_UPLOAD_BTN,
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        opacity: 1,
        depth: this.getElementShouldDepth(pageID),
        objHasModified: objHasModified,
        belongToPage: pageID
      };

      if (config) {
        const keys = Object.keys(imgUploadBtn) as (keyof IImgUploadBtn)[];
        keys.splice(keys.indexOf("ID"), 1);
        for (const key of keys) {
          if (config[key] === undefined) {
            continue;
          }
          (imgUploadBtn[key] as string | number | boolean) = config[key];
        }
      }

      page.imgUploadBtns[imgUploadBtnID] = imgUploadBtn;
      return AddElementResult.OK;
    },
    setPageThumbnail(PageID: number, thumbnail: string | undefined) {
      const page = this.getPage(PageID);
      if (!page) {
        return;
      }

      page.thumbnail = thumbnail;
    },
    setPageSelected(pageID: number, selected: string | undefined) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      page.selected = selected;
    },
    getPageSelected(pageID: number) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      if (!page.selected) {
        return;
      }
      if (page.bg?.ID === page.selected) {
        return page.bg;
      }

      if (page.selected in page.texts) {
        return page.texts[page.selected];
      }
      if (page.selected in page.graphics) {
        return page.graphics[page.selected];
      }
      if (page.selected in page.images) {
        return page.images[page.selected];
      }
    },
    setPageClipped(pageID: number, clipped: string | undefined) {
      const page = this.getPage(pageID);
      if (!page) {
        return;
      }

      page.clipped = clipped;
    }
  }
});

const _onBeforeSwitchPageCallbacks: {
  [key: string]: ((pageID: number) => void)[];
} = { 0: [] };
export const executeBeforeSwitchPageCallbacks = (pageID: number) => {
  const keys = Object.keys(_onBeforeSwitchPageCallbacks).sort();
  for (const key of keys) {
    _onBeforeSwitchPageCallbacks[key].forEach((callback) => {
      try {
        callback(pageID);
      } catch {}
    });
  }
};
export const onBeforeSwitchPage = (
  callback: (pageID: number) => void,
  order: number = 0
) => {
  if (!(order in _onBeforeSwitchPageCallbacks)) {
    _onBeforeSwitchPageCallbacks[order] = [];
  }

  _onBeforeSwitchPageCallbacks[order].push(callback);
};
