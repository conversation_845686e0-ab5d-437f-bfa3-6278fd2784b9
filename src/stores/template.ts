/*
 * @Author: ha<PERSON><PERSON><PERSON> han<PERSON>@zuoyebang.com
 * @Date: 2024-10-31 17:03:56
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-23 18:05:10
 * @Description: 记录页面是否有修改、导出
 */

import { defineStore } from "pinia";
import type { TemplateConfig } from "@/template";

export const useTemplateStore = defineStore("template", {
  state: (): { template: undefined | TemplateConfig } => {
    return { template: undefined };
  },
  getters: {
    isTemplateSet: (state) => {
      return !!state.template;
    }
  },
  actions: {
    setTemplate(templatePageConfig: TemplateConfig) {
      console.log(123);
      this.template = templatePageConfig;
    }
  }
});
