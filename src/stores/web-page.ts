/*
 * @Author: ha<PERSON><PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-31 17:03:56
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-05 11:23:17
 * @Description: 记录页面是否有修改、导出
 */

import { defineStore } from "pinia";
import type { TipsBannerType } from "@/types/tips-banner";
import { getUUID } from "@/utils/get-uuid";
import { CANVAS_DEFAULT_SCALE } from "@/constants";

export enum ExportType {
  PDF = 1,
  PNG
}

export interface TipsConfig {
  type?: TipsBannerType;
  text: string;
  duration?: number;
  isOverlay?: boolean;
}

export interface TipsCompleteConfig extends TipsConfig {
  readonly ID: string;
  isShow: boolean;
}

export const usePageStore = defineStore("page", {
  state: (): {
    hasExported: boolean;
    hasModified: boolean;
    pendingToExport: ExportType | undefined;
    isExporting: boolean;
    tips: TipsCompleteConfig[];
    canvasScale: number;
  } => {
    return {
      hasExported: false,
      hasModified: false,
      pendingToExport: undefined,
      isExporting: false,
      tips: [],
      canvasScale: CANVAS_DEFAULT_SCALE
    };
  },
  actions: {
    setHasExported(value: boolean = true) {
      this.hasExported = value;
    },
    setHasModified(value: boolean = true) {
      this.hasModified = value;
    },
    setPendingToExport(value: ExportType | undefined = undefined) {
      if (!value) {
        this.pendingToExport = undefined;
        return;
      }

      if (this.isExporting) {
        return;
      }

      this.pendingToExport = value;
    },
    setIsExporting(value: boolean = true) {
      this.isExporting = value;
    },
    addTips(config: TipsConfig) {
      const ID = getUUID();
      const completeConfig = {
        ID: ID,
        isShow: true,
        ...config
      };
      this.tips.unshift(completeConfig);
      return ID;
    },
    removeTips(ID: string) {
      const index = this.tips.findIndex((item) => item.ID === ID);
      if (index !== -1) {
        this.tips.splice(index, 1);
      }
    },
    updateCanvasScale(scale: number) {
      this.canvasScale = scale;
    }
  }
});
