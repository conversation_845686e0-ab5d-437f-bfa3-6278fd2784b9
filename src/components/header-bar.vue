<!--
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-29 16:34:30
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-01-20 17:10:13
 * @Description:
-->
<script setup lang="ts">
import { ref, nextTick } from "vue";
import {
  BOOK_PAGE_MAX_QUANTITY,
  GRAPHICS,
  IMAGE_MAX_QUANTITY
} from "@/constants";
import { useBookStore } from "@/stores/book";
import { AddElementResult } from "@/types/book";
import { usePageStore, ExportType } from "@/stores/web-page";
import ImageInput from "@/components/image-input.vue";
import LoadResult from "@/types/load-result-type";
import BackIcon from "@/components/icons/back-icon.vue";

const pageStore = usePageStore();
const bookStore = useBookStore();

const addText = () => {
  const result = bookStore.addPageText(bookStore.currentPageID);

  if (result === AddElementResult.OVER_MAX_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "文本数量超出限制"
    });
    return;
  }
  pageStore.setHasModified();
};

const addGraphics = (index: number) => {
  const graphics = GRAPHICS[index];
  const result = bookStore.addPageGraphics(bookStore.currentPageID, {
    src: graphics.url,
    opacity: graphics.opacity
  });

  if (result === AddElementResult.OVER_MAX_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "图形数量超出限制"
    });
    return;
  }
  pageStore.setHasModified();
};

const bgInputActive = ref(false);
const bgInputQuantity = ref(1);
const showBGInputBox = () => {
  if (bookStore.pages.length >= BOOK_PAGE_MAX_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "页面数量超出限制"
    });
    return;
  }

  bgInputQuantity.value = BOOK_PAGE_MAX_QUANTITY - bookStore.pages.length;

  bgInputActive.value = true;
  nextTick(() => {
    bgInputActive.value = false;
  });
};
let bgTipsLoadingID: string | undefined;
const onUploadBG = () => {
  bgTipsLoadingID = pageStore.addTips({
    type: "loading",
    text: "正在加载图片",
    duration: -1,
    isOverlay: true
  });
};
const onUploadedBG = (
  data: string | undefined,
  result?: LoadResult | undefined
) => {
  if (!data) {
    switch (result) {
      case LoadResult.IS_LOADING:
        pageStore.addTips({
          type: "warning",
          text: "已有图片正在加载中，请稍后再试"
        });
        break;
      case LoadResult.NO_FILE:
        pageStore.addTips({
          text: "未选择图片"
        });
        break;
      case LoadResult.OVER_MAX_QUANTITY:
        pageStore.addTips({
          type: "warning",
          text: "页面数量超出限制"
        });
        break;
      case LoadResult.OVER_MAX_SIZE:
        pageStore.addTips({
          type: "warning",
          text: "图片大小超出限制"
        });
        break;
      case LoadResult.NOT_IMAGE:
        pageStore.addTips({
          type: "warning",
          text: "非图片文件"
        });
        break;
      case LoadResult.ALL_COMPLETE:
        if (bgTipsLoadingID) {
          pageStore.removeTips(bgTipsLoadingID);
          bgTipsLoadingID = undefined;
        }
        break;
      default:
        pageStore.addTips({
          type: "error",
          text: "图片加载失败，稍后再试试"
        });
        break;
    }
    return;
  }

  bookStore.addPrePageWithBG(data);
  pageStore.addTips({
    text: "导入完成"
  });
  pageStore.setHasModified();
};

const imageInputActive = ref(false);
const imageInputQuantity = ref(1);
const showImageInputBox = () => {
  const pageImages = bookStore.getPage(bookStore.currentPageID)?.images;
  if (!pageImages) {
    return;
  }

  const length = Object.keys(pageImages).length;
  if (length >= IMAGE_MAX_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "图片数量超出限制"
    });
    return;
  }

  imageInputQuantity.value = IMAGE_MAX_QUANTITY - length;

  imageInputActive.value = true;
  nextTick(() => {
    imageInputActive.value = false;
  });
};
let imageTipsLoadingID: string | undefined;
const onUploadImage = () => {
  imageTipsLoadingID = pageStore.addTips({
    type: "loading",
    text: "正在加载图片",
    duration: -1,
    isOverlay: true
  });
};
const onUploadedImage = (
  data: string | undefined,
  result?: LoadResult | undefined
) => {
  if (!data) {
    switch (result) {
      case LoadResult.IS_LOADING:
        pageStore.addTips({
          type: "warning",
          text: "已有图片正在加载中，请稍后再试"
        });
        break;
      case LoadResult.NO_FILE:
        pageStore.addTips({
          text: "未选择图片"
        });
        break;
      case LoadResult.OVER_MAX_QUANTITY:
        pageStore.addTips({
          type: "warning",
          text: "图片数量超出限制"
        });
        break;
      case LoadResult.OVER_MAX_SIZE:
        pageStore.addTips({
          type: "warning",
          text: "图片大小超出限制"
        });
        break;
      case LoadResult.NOT_IMAGE:
        pageStore.addTips({
          type: "warning",
          text: "非图片文件"
        });
        break;
      case LoadResult.ALL_COMPLETE:
        if (imageTipsLoadingID) {
          pageStore.removeTips(imageTipsLoadingID);
          imageTipsLoadingID = undefined;
        }

        break;
      default:
        pageStore.addTips({
          type: "error",
          text: "图片加载失败，稍后再试试"
        });
        break;
    }
    return;
  }

  bookStore.addPageImage(bookStore.currentPageID, {
    src: data
  });

  pageStore.addTips({
    text: "已添加图片"
  });
  pageStore.setHasModified();
};

let canImport = true;
const debounceDuration = 1000;
const download = (value: ExportType) => {
  if (bookStore.pages.length === 0 || !canImport) {
    return;
  }

  pageStore.setPendingToExport(value);
  canImport = false;
  setTimeout(() => {
    canImport = true;
  }, debounceDuration);
};
</script>

<template>
  <v-app-bar class="header-bar" height="61">
    <template v-slot:prepend>
      <v-app-bar-nav-icon
        class="header-bar__back-icon"
        variant="plain"
        to="/"
        :ripple="false"
      >
        <v-icon :icon="BackIcon"></v-icon>
        <span>返回</span>
      </v-app-bar-nav-icon>
    </template>
    <v-spacer></v-spacer>
    <v-btn
      class="header-bar__function-btn"
      variant="outlined"
      @click="showBGInputBox"
    >
      <template #prepend>
        <img
          class="header-bar__function-btn-icon"
          src="@/assets/icons/import.svg"
        /> </template
      >一键导入</v-btn
    >
    <image-input
      :active="bgInputActive"
      :max-quantity="bgInputQuantity"
      @update:upload="onUploadBG"
      @update:uploaded="onUploadedBG"
      multiple
    ></image-input>
    <v-btn class="header-bar__function-btn" variant="outlined" @click="addText">
      <template #prepend>
        <img
          class="header-bar__function-btn-icon"
          src="@/assets/icons/text.svg"
        />
      </template>
      添加文本
    </v-btn>
    <v-menu :location="'bottom center'">
      <template v-slot:activator="{ props }">
        <v-btn
          class="header-bar__function-btn"
          variant="outlined"
          v-bind="props"
        >
          <template #prepend>
            <img
              class="header-bar__function-btn-icon"
              src="@/assets/icons/graphics.svg"
            />
          </template>
          添加图形
        </v-btn>
      </template>
      <div class="header-bar__graphics-menu-border">
        <div class="header-bar__graphics-menu-content">
          <img
            class="header-bar__graphics-menu-item"
            v-for="(item, index) in GRAPHICS"
            :key="index"
            :src="item.thumbnail"
            @click="addGraphics(index)"
          />
        </div>
      </div>
    </v-menu>
    <v-btn
      class="header-bar__function-btn"
      variant="outlined"
      @click="showImageInputBox"
    >
      <template #prepend>
        <img
          class="header-bar__function-btn-icon"
          src="@/assets/icons/image.svg"
        /> </template
      >添加图片</v-btn
    >
    <image-input
      :active="imageInputActive"
      :max-quantity="imageInputQuantity"
      @update:upload="onUploadImage"
      @update:uploaded="onUploadedImage"
      multiple
    ></image-input>
    <v-spacer></v-spacer>
    <v-menu k>
      <template v-slot:activator="{ props }">
        <v-btn
          class="header-bar__function-btn mr-5"
          variant="outlined"
          width="150"
          v-bind="props"
        >
          <template #prepend>
            <img
              class="header-bar__function-btn-icon"
              src="@/assets/icons/export.svg"
            />
          </template>
          合成
        </v-btn>
      </template>
      <v-list>
        <v-list-item value="png"
          ><v-list-item-title @click="download(ExportType.PNG)"
            >图片格式 PNG</v-list-item-title
          >
        </v-list-item>
        <v-list-item value="pdf"
          ><v-list-item-title @click="download(ExportType.PDF)"
            >文档格式 PDF</v-list-item-title
          ></v-list-item
        >
      </v-list>
    </v-menu>
  </v-app-bar>
</template>

<style>
.header-bar .header-bar__back-icon {
  margin-right: 0.875rem;
  margin-left: 2rem;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 0.93rem;
  font-weight: 400;
  letter-spacing: 0;
  opacity: 1;
}

.header-bar .header-bar__back-icon:hover {
  color: rgb(var(--v-theme-on-surface-text-hover));
}

.header-bar .header-bar__back-icon:hover:active {
  color: rgb(var(--v-theme-on-surface-text-active));
}

.header-bar .header-bar__function-btn {
  margin-right: 24px;
  border: 0.5px solid rgb(var(--v-border-color));
  border-radius: 10px;
  padding: 0 26px;
  font-family: PingFangSC-Regular, sans-serif;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface));
}

.header-bar .header-bar__function-btn:hover {
  background-color: rgb(var(--v-theme-on-surface-btn-hover));
}

.header-bar .header-bar__function-btn:active {
  background-color: rgb(var(--v-theme-on-surface-btn-active));
}

@media (width <= 900px) {
  .header-bar .header-bar__function-btn-icon {
    display: none;
  }

  .header-bar .header-bar__function-btn {
    margin-right: 5px;
    padding: 0 12px;
  }
}

.header-bar__graphics-menu-border {
  position: relative;
  margin-top: 12px;
  border: 0.5px solid rgb(var(--v-theme-on-surface-darken-1-border-color));
  border-radius: 10px;
  padding: 24px;
  width: 21.25rem;
  max-width: 21.25rem;
  height: 14.125rem;
  background-color: rgb(var(--v-theme-surface-darken-1));
}

.header-bar__graphics-menu-border::before,
.header-bar__graphics-menu-border::after {
  position: absolute;
  top: -20px;
  left: 50%;
  border-top: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid rgb(var(--v-theme-on-surface-darken-1-border-color));
  border-left: 10px solid transparent;
  width: 0;
  height: 0;
  transform: translateX(-50%);
  content: "";
}

.header-bar__graphics-menu-border::after {
  top: -19px;
  border-bottom: 10px solid rgb(var(--v-theme-surface-darken-1));
}

.header-bar__graphics-menu-content {
  display: flex;
  width: 100%;
  height: 100%;
  flex-wrap: wrap;
  justify-content: space-between;
}

.header-bar__graphics-menu-item {
  width: 24%;
  object-fit: none;
  cursor: pointer;
}
</style>
