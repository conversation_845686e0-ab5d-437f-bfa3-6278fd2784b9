<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-29 16:35:12
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2024-12-09 14:15:30
 * @Description:
-->
<script setup lang="ts">
import { computed, nextTick, useTemplateRef } from "vue";
import { usePageStore } from "@/stores/web-page";
import { useBookStore } from "@/stores/book";
import { AddElementResult } from "@/types/book";
import { RemoveElementResult } from "@/types/book";
import THUMBNAIL_16_9 from "@/assets/images/thumbnail-16-9.png";
import THUMBNAIL_1_1 from "@/assets/images/thumbnail-1-1.png";
import THUMBNAIL_9_16 from "@/assets/images/thumbnail-9-16.png";

const pageStore = usePageStore();
const bookStore = useBookStore();

const getThumbnail = (ratio: number) => {
  if (ratio > 1) {
    return THUMBNAIL_16_9;
  } else if (ratio < 1) {
    return THUMBNAIL_9_16;
  } else {
    return THUMBNAIL_1_1;
  }
};
const THUMBNAIL = getThumbnail(bookStore.width / bookStore.height);

const selectedPageID = computed({
  get: () => {
    return [bookStore.currentPageID];
  },
  set: (val) => {
    bookStore.setCurrentPage(val[0]);
  }
});
const listBottomTagRef = useTemplateRef("listBottomTag");
const addPage = () => {
  const result = bookStore.addPage();
  if (result === AddElementResult.OVER_MAX_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "页面数量超出限制"
    });
    return;
  }

  bookStore.setCurrentPage(bookStore.lastPageID);

  nextTick(() => {
    listBottomTagRef.value?.scrollIntoView(false);
    setTimeout(() => {
      listBottomTagRef.value?.scrollIntoView(false);
    }, 10);
  });
};

const removeBookPage = (index: number) => {
  const result = bookStore.removePage(index);
  if (result === RemoveElementResult.OVER_MIN_QUANTITY) {
    pageStore.addTips({
      type: "warning",
      text: "最少要有一个书本页～"
    });
  }
};
</script>

<template>
  <v-navigation-drawer class="left-nav" permanent touchless width="150">
    <div class="left-nav__nav-list">
      <v-list lines="three" nav v-model:selected="selectedPageID" mandatory>
        <v-list-item
          class="left-nav__nav-item"
          :color="
            '' +
            $vuetify.theme.global.current.colors['on-surface-lighten-1-active']
          "
          :value="item.ID"
          v-for="item in bookStore.pages"
          :key="item.ID"
        >
          <v-img
            max-height="120"
            :src="item.thumbnail ? item.thumbnail : THUMBNAIL"
            :draggable="false"
          ></v-img>

          <v-btn
            class="left-nav__close-btn"
            size="25"
            icon="mdi-close"
            @click.stop="removeBookPage(item.ID)"
          ></v-btn>
        </v-list-item>
      </v-list>
      <div ref="listBottomTag" style="height: 1px"></div>
    </div>
    <button class="left-nav__add-btn" @click="addPage" v-ripple>+</button>
  </v-navigation-drawer>
</template>

<style>
.left-nav {
  margin-top: 2px;
  padding-bottom: 3rem;
  background: rgb(var(--v-theme-surface-lighten-1)) !important;
}

.left-nav__nav-list {
  overflow: hidden auto;
  height: 100%;
}

.left-nav__close-btn {
  display: none;
  position: absolute;
  top: 0.5rem;
  right: 0.2rem;
}

.left-nav__nav-item:hover .left-nav__close-btn {
  display: inline-grid;
}

.left-nav__add-btn {
  position: fixed;
  bottom: 2px;
  border-top: 1px solid rgb(var(--v-theme-background));
  width: 100%;
  height: 3rem;
  font-size: 2rem;
  color: rgb(var(--v-theme-primary));
  background-color: rgb(var(--v-theme-surface-lighten-1));
}
</style>
