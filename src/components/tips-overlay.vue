<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-11-28 17:28:33
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2024-11-29 15:02:32
 * @Description:
-->
<script setup lang="ts">
import { ref, watch } from "vue";
import TipsBanner from "@/components/tips-banner.vue";
import { usePageStore } from "@/stores/web-page";

const pageStore = usePageStore();

let timer: number | undefined;
const isShowSelf = ref(false);

const isShowOverlay = ref(false);

watch(pageStore.tips, () => {
  let _isShowOverlay = false;
  const pendingToRemove = [];
  for (let i = pageStore.tips.length - 1; i >= 0; i--) {
    const item = pageStore.tips[i];
    if (!item.isShow) {
      pendingToRemove.push(item.ID);
      continue;
    }
    if (item.isOverlay) {
      _isShowOverlay = true;
    }
  }

  pendingToRemove.forEach((ID) => {
    pageStore.removeTips(ID);
  });

  clearTimeout(timer);
  if (pageStore.tips.length > 0) {
    isShowSelf.value = true;
  } else {
    timer = setTimeout(() => {
      isShowSelf.value = false;
    }, 1000);
  }

  isShowOverlay.value = _isShowOverlay;
});

// setTimeout(() => {
//   pageStore.addTips({
//     text: "This is a tips overlay1",
//     isOverlay: false,
//     duration: 2000,
//     type: "info"
//   });
//   pageStore.addTips({
//     text: "This is a tips overlay2",
//     isOverlay: false,
//     duration: 1000,
//     type: "warning"
//   });
//   pageStore.addTips({
//     text: "This is a tips overlay3",
//     isOverlay: false,
//     duration: 3000,
//     type: "loading"
//   });
// }, 2000);
// setTimeout(() => {
//   pageStore.addTips({
//     text: "This is a tips overlay4",
//     isOverlay: false,
//     duration: 2000,
//     type: "warning"
//   });
// }, 4000);

// setTimeout(() => {
//   pageStore.addTips({
//     text: "This is a tips overlay",
//     isOverlay: false,
//     duration: 3000,
//     type: "error"
//   });
// }, 6000);
// setTimeout(() => {
//   pageStore.addTips({
//     text: "This is a tips overlay",
//     isOverlay: true,
//     duration: 4000,
//     type: "loading"
//   });
// }, 8000);
</script>

<template>
  <Teleport to="body">
    <div class="tips-overlay" v-show="isShowSelf">
      <div class="tips-overlay__overlay" v-show="isShowOverlay"></div>
      <TransitionGroup name="tips-overlay__fade">
        <tips-banner
          v-model:show="item.isShow"
          :duration="item.duration"
          :type="item.type"
          :key="item.ID"
          v-for="item in pageStore.tips"
          >{{ item.text }}</tips-banner
        >
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<style>
.tips-overlay {
  display: flex;
  position: fixed;
  z-index: 3000;
  padding-top: 1rem;
  inset: 0;
  flex-direction: column;
  align-items: center;
  pointer-events: none;
}

.tips-overlay__overlay {
  position: fixed;
  inset: 0;
  pointer-events: all;
}

.tips-overlay__fade-move,
.tips-overlay__fade-enter-active,
.tips-overlay__fade-leave-active {
  transition: all 0.5s ease;
}

.tips-overlay__fade-enter-from {
  opacity: 0;
  transform: translateY(-40px);
}

.tips-overlay__fade-leave-to {
  opacity: 0;
  transform: translateX(40px);
}

.tips-overlay__fade-leave-active {
  position: absolute;
}
</style>
