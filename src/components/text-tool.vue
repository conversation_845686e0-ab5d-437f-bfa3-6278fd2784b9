<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-29 16:36:26
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2025-01-20 16:10:30
 * @Description:
-->
<script setup lang="ts">
import { computed, ref } from "vue";
import { useBookStore } from "@/stores/book";
import { usePageStore } from "@/stores/web-page";
import { ElementType, type ITextboxData } from "@/types/book";
import {
  TEXT_COLOR_RANGE,
  TEXT_DEFAULT_COLOR,
  TEXT_MIN_FONTSIZE,
  TEXT_MAX_FONTSIZE,
  TEXT_DEFAULT_FONTSIZE,
  TEXT_MIN_OPACITY,
  TEXT_MAX_OPACITY,
  TEXT_DEFAULT_OPACITY,
  TEXT_FONT_FAMILY,
  TEXT_DEFAULT_FONT_FAMILY
} from "@/constants";
import getLimitedValue from "@/utils/get-limited-value";
import {
  checkFontFamilyIsLoaded,
  loadFontFamily
} from "@/utils/handle-font-family";

const bookStore = useBookStore();
const pageStore = usePageStore();

const _textDirection = ref(0);
const textDireciton = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      if (data.angle === 0 || data.angle === 90) {
        return data.angle;
      }
    }

    return _textDirection.value;
  },
  set: (value: number) => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      bookStore.updatePageText(bookStore.currentPageID, data.ID, {
        angle: value
      });
    }
    _textDirection.value = value;
  }
});

const _fontFamily = ref([TEXT_DEFAULT_FONT_FAMILY]);
loadFontFamily(
  TEXT_DEFAULT_FONT_FAMILY,
  TEXT_FONT_FAMILY[TEXT_DEFAULT_FONT_FAMILY].url
).catch(() => {});
const fontFamily = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    let font = _fontFamily.value;
    if (data && data.type === ElementType.TEXTBOX) {
      if ((data as ITextboxData).fontFamily in TEXT_FONT_FAMILY) {
        font = [(data as ITextboxData).fontFamily];
      } else {
        font = [TEXT_DEFAULT_FONT_FAMILY];
      }
    }

    return font;
  },
  set: (value: string[]) => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      if (
        TEXT_FONT_FAMILY[value[0] as keyof typeof TEXT_FONT_FAMILY].value
          .length > 0 ||
        checkFontFamilyIsLoaded(value[0])
      ) {
        bookStore.updatePageText(bookStore.currentPageID, data.ID, {
          fontFamily: value[0]
        });
      } else {
        const originalData = data as ITextboxData;

        const tipsLoadingID = pageStore.addTips({
          type: "loading",
          text: "正在加载字体，请稍候",
          duration: -1,
          isOverlay: true
        });
        loadFontFamily(
          value[0],
          TEXT_FONT_FAMILY[value[0] as keyof typeof TEXT_FONT_FAMILY].url
        )
          .then(() => {
            if (
              originalData ===
              bookStore.getPageSelected(bookStore.currentPageID)
            ) {
              bookStore.updatePageText(bookStore.currentPageID, data.ID, {
                fontFamily: value[0]
              });

              pageStore.addTips({
                text: "字体加载完成"
              });
            }
          })
          .catch(() => {
            //  待添加 tips : 加载字体失败
            pageStore.addTips({
              type: "error",
              text: "字体加载失败，再重新试试吧"
            });
          })
          .finally(() => {
            if (tipsLoadingID) {
              pageStore.removeTips(tipsLoadingID);
            }
          });
      }
    }
    _fontFamily.value = value;
  }
});

const _fontColor = ref(TEXT_DEFAULT_COLOR);
const fontColor = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      return (data as ITextboxData).color;
    }

    return _fontColor.value;
  },
  set: (value: string) => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      bookStore.updatePageText(bookStore.currentPageID, data.ID, {
        color: value
      });
    }
    _fontColor.value = value;
  }
});
const onSelectColor = (color: string) => {
  fontColor.value = color;
};
const isShowColorMenu = ref(false);
const toggleColorMenu = () => {
  isShowColorMenu.value = !isShowColorMenu.value;
};

const _fontSize = ref(TEXT_DEFAULT_FONTSIZE);
const fontSize = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    let size = _fontSize.value;

    if (data && data.type === ElementType.TEXTBOX) {
      size = (data as ITextboxData).fontSize;
    }

    return +getLimitedValue(size, TEXT_MIN_FONTSIZE, TEXT_MAX_FONTSIZE).toFixed(
      1
    );
  },
  set: (value: number) => {
    value = +value;
    if (Number.isNaN(value)) {
      return;
    }

    value = +getLimitedValue(
      value,
      TEXT_MIN_FONTSIZE,
      TEXT_MAX_FONTSIZE
    ).toFixed(1);

    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      bookStore.updatePageText(bookStore.currentPageID, data.ID, {
        fontSize: value
      });
    }
    _fontSize.value = value;
  }
});

const _fontOpacity = ref(TEXT_DEFAULT_OPACITY);
const fontOpacity = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    let opacity = _fontOpacity.value;

    if (data && data.type === ElementType.TEXTBOX) {
      opacity = (data as ITextboxData).opacity;
    }
    return +getLimitedValue(
      opacity,
      TEXT_MIN_OPACITY,
      TEXT_MAX_OPACITY
    ).toFixed(1);
  },
  set: (value: number) => {
    value = +value;
    if (Number.isNaN(value)) {
      return;
    }

    value = +getLimitedValue(value, TEXT_MIN_OPACITY, TEXT_MAX_OPACITY).toFixed(
      1
    );

    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && data.type === ElementType.TEXTBOX) {
      bookStore.updatePageText(bookStore.currentPageID, data.ID, {
        opacity: value
      });
    }
    _fontOpacity.value = value;
  }
});

const remove = () => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data || !(data.type === ElementType.TEXTBOX)) {
    return;
  }

  bookStore.removeElement(bookStore.currentPageID, data.ID);
};
</script>

<template>
  <div class="text-tool">
    <v-btn-toggle
      class="text-tool__direction-btn-group"
      v-model="textDireciton"
      variant="outlined"
      v-if="false"
      mandatory
    >
      <v-btn
        class="text-tool__direction-btn"
        selected-class="text-tool__direction-btn--active"
        :value="0"
      >
        <template #prepend>
          <img src="@/assets/icons/text-direction-l.svg" alt="" />
        </template>
        横向
      </v-btn>

      <v-btn
        class="text-tool__direction-btn"
        selected-class="text-tool__direction-btn--active"
        :value="90"
        ><template #prepend>
          <img src="@/assets/icons/text-direction-p.svg" alt="" />
        </template>
        竖向
      </v-btn>
    </v-btn-toggle>
    <v-divider class="mt-4" v-if="false"></v-divider>
    <div class="text-tool__font-family">
      <span class="text-tool__font-family-label"> 字体 </span>
      <div class="text-tool__font-family-menu">
        <v-menu>
          <template v-slot:activator="{ props }">
            <v-btn
              class="text-tool__font-family-selector"
              variant="outlined"
              v-bind="props"
              :ripple="false"
            >
              {{
                TEXT_FONT_FAMILY[fontFamily[0] as keyof typeof TEXT_FONT_FAMILY]
                  .name
              }}
              <template #append>
                <div class="text-tool__font-family-selector-arrow"></div>
              </template>
            </v-btn>
          </template>
          <v-list
            class="text-tool__font-family-menu-list"
            v-model:selected="fontFamily"
            mandatory
          >
            <v-list-item
              class="text-tool__font-family-menu-list-item"
              :value="key"
              slim
              v-for="(item, key) in TEXT_FONT_FAMILY"
              :key="key"
              >{{ item.name }}</v-list-item
            >
          </v-list>
        </v-menu>
      </div>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div class="text-tool__color">
      <div class="text-tool__color-title">
        <span class="text-tool__color-label"> 颜色 </span>
        <span class="text-tool__color-btn">
          <span
            class="text-tool__color-btn-color"
            :style="{ background: fontColor }"
            @click="toggleColorMenu"
          ></span>
          <span class="text-tool__color-btn-icon"></span>
        </span>
      </div>
      <transition name="text-tool__color-menu">
        <div class="text-tool__color-menu" v-show="isShowColorMenu">
          <div
            class="text-tool__color-menu-item"
            :value="item"
            v-for="(item, index) in TEXT_COLOR_RANGE"
            :key="index"
            :style="{ background: item }"
            @click="onSelectColor(item)"
          ></div>
        </div>
      </transition>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div class="text-tool__font-size">
      <span class="text-tool__font-family-label"> 大小 </span>
      <v-slider
        class="text-tool__font-size-slider"
        v-model.number="fontSize"
        :min="TEXT_MIN_FONTSIZE"
        :max="TEXT_MAX_FONTSIZE"
        :step="1"
        thumb-size="18"
        hide-spin-buttons
        hide-details
      >
        <template v-slot:append>
          <input
            class="text-tool__font-size-input"
            v-model.number="fontSize"
            type="number"
            step="1"
            :min="TEXT_MIN_FONTSIZE"
            :max="TEXT_MAX_FONTSIZE"
          />
        </template>
      </v-slider>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div class="text-tool__font-opacity">
      <span class="text-tool__font-opacity-label"> 不透明度 </span>
      <v-slider
        class="text-tool__font-opacity-slider"
        v-model.number="fontOpacity"
        :min="TEXT_MIN_OPACITY"
        :max="TEXT_MAX_OPACITY"
        :step="0.1"
        thumb-size="18"
        hide-spin-buttons
        hide-details
      >
        <template v-slot:append>
          <input
            class="text-tool__font-opacity-input"
            v-model.number="fontOpacity"
            type="number"
            step="0.1"
            :min="TEXT_MIN_OPACITY"
            :max="TEXT_MAX_OPACITY"
          />
        </template>
      </v-slider>
    </div>
    <v-divider class="mt-4"></v-divider>
    <v-btn class="text-tool__delete" variant="outlined" @click="remove">
      <template #prepend> <img src="@/assets/icons/delete.svg" /></template>
      删除
    </v-btn>
  </div>
</template>

<style>
.text-tool .text-tool__direction-btn-group {
  border: 0.6px solid rgb(var(--v-theme-on-surface-lighten-1-btn-border));
  border-radius: 10px;
  padding: 1px;
  width: 100%;
  height: 40px;
  color: rgb(var(--v-theme-on-surface-lighten-1));
  background: rgb(var(--v-theme-on-surface-lighten-1-btn-bg));
  box-shadow:
    0 0 0 0 rgb(var(--v-theme-on-surface-lighten-1-btn-shadow)),
    inset 0 1px 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-shadow-inset));
}

.text-tool__direction-btn {
  border: 0;
  flex: 1 1 auto;
}

.text-tool .text-tool__direction-btn-group .text-tool__direction-btn--active {
  color: rgb(var(--v-theme-on-surface-lighten-1));
  background: rgb(var(--v-theme-on-surface-lighten-1-active-2));
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0
      rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-inset));
}

.text-tool__color,
.text-tool__font-size,
.text-tool__font-opacity {
  padding-top: 1.5rem;
}

.text-tool__font-family-label,
.text-tool__color-label,
.text-tool__font-size-label,
.text-tool__font-opacity-label {
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
}

.text-tool__font-family-menu {
  margin-top: 1rem;
}

.text-tool__font-family-selector {
  position: relative;
  border: 0;
  border-radius: 10px;
  width: 100%;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
  background: rgb(var(--v-theme-on-surface-lighten-1-active-2));
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0
      rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-inset)),
    0 0 2px 0 #1a1a1a,
    0 0 2px 0 #1a1a1a,
    0 0 2px 0 #1a1a1a;
  justify-content: left;
}

.text-tool__font-family-selector-arrow {
  position: absolute;
  right: 2px;
  border-radius: 8px;
  width: 2rem;
  height: 90%;
  background: rgb(var(--v-theme-on-surface-lighten-1-arrow));
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0 rgb(var(--v-theme-on-surface-lighten-1-btn-shadow-inset-1));
}

.text-tool__font-family-selector-arrow::before {
  position: absolute;
  top: 50%;
  left: 50%;
  border-top: 2px solid rgb(var(--v-theme-on-surface-lighten-1));
  border-left: 2px solid rgb(var(--v-theme-on-surface-lighten-1));
  border-radius: 2px;
  width: 25%;
  height: 25%;
  transform: translate(-50%, -50%) rotate(-135deg);
  transform-origin: center center;
  content: "";
}

.text-tool__font-family-menu-list {
  margin-top: 3px;
  border: 0.5px solid rgb(var(--v-theme-on-surface-text-active));
  border-radius: 10px !important;
  font-family: PingFangSC-Light, sans-serif;
  font-size: 1rem;
  font-weight: 200;
  letter-spacing: 0;
  background: rgb(var(--v-theme-surface-darken-3)) !important;
  box-shadow: 0 0 16px 0 rgb(0 0 0 / 50%) !important;
}

.text-tool__font-family-menu-list .text-tool__font-family-menu-list-item {
  height: 2rem;
  min-height: 2rem;
  color: rgb(var(--v-theme-on-surface-darken-3));
}

.text-tool__color-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-tool__color-btn {
  overflow: hidden;
  border-radius: 10px;
  width: 50%;
  height: 2rem;
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0
      rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-inset)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1));
}

.text-tool__color-btn-color {
  display: inline-block;
  width: 65%;
  height: 100%;
  cursor: pointer;
}

.text-tool__color-btn-icon {
  display: inline-block;
  position: relative;
  width: 35%;
  height: 100%;
  background: rgb(var(--v-theme-on-surface-lighten-1-active-2));
}

.text-tool__color-btn-icon::before {
  position: absolute;
  top: 1px;
  left: 50%;
  font-family: MF-YuanHeiNoncommercial-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
  transform: translateX(-50%);
  content: "A";
}

.text-tool__color-btn-icon::after {
  position: absolute;
  bottom: 6px;
  left: 50%;
  border-radius: 2px;
  width: 40%;
  height: 3px;
  background: rgb(var(--v-theme-on-surface-lighten-1));
  background-image: linear-gradient(90deg, #fb0 0%, #ff5c00 89%);
  transform: translateX(-50%);
  content: "";
}

.text-tool__color-menu {
  display: flex;
  position: relative;
  margin-top: 10px;
  border: 0.5px solid rgb(var(--v-theme-on-surface-text-active));
  border-radius: 10px;
  padding: 1rem;
  width: 100%;
  height: 90px;
  background: rgb(var(--v-theme-surface-darken-3));
  box-shadow: 0 0 16px 0 rgb(0 0 0 / 50%);
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

.text-tool__color-menu::before,
.text-tool__color-menu::after {
  position: absolute;
  top: -20px;
  left: 65%;
  border-top: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid rgb(var(--v-theme-on-surface-text-active));
  border-left: 10px solid transparent;
  width: 0;
  height: 0;
  content: "";
}

.text-tool__color-menu::after {
  top: -19px;
  border-bottom: 10px solid rgb(var(--v-theme-surface-darken-3));
}

.text-tool__color-menu-item {
  border: 0.4px solid currentcolor;
  width: 2rem;
  height: 1.5rem;
  cursor: pointer;
}

.text-tool__color-menu-enter-active,
.text-tool__color-menu-leave-active {
  transition: transform 0.3s ease;
  transform-origin: 65% 0;
}

.text-tool__color-menu-enter-from,
.text-tool__color-menu-leave-to {
  transform: scale(0);
}

.text-tool__font-size-slider,
.text-tool__font-opacity-slider {
  margin: 0 !important;
}

.text-tool__font-size-input,
.text-tool__font-opacity-input {
  border: 0.3px solid rgb(var(--v-theme-on-surface-lighten-1-input-border));
  padding: 0 0.5rem;
  width: 3.75rem;
  height: 1.75rem;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
  background: rgb(var(--v-theme-on-surface-lighten-1-input));
  outline: 0;
  box-shadow:
    inset 0 1px 2px 0
      rgb(var(--v-theme-on-surface-lighten-1-input-border-shadow)),
    inset 0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-active-2));
}

.text-tool__delete {
  margin-top: 1.5rem;
  border: 0;
  border-radius: 10px;
  width: 100%;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-darken-3));
  background: rgb(var(--v-theme-on-surface-lighten-1-active-2));
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0
      rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-inset)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1));
}
</style>
