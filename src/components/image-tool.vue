<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-29 16:36:26
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2025-01-20 15:18:03
 * @Description:
-->
<script setup lang="ts">
import { computed, ref, nextTick } from "vue";
import getLimitedValue from "@/utils/get-limited-value";
import {
  IMG_SCALE_MIN_VALUE,
  IMG_SCALE_MAX_VALUE,
  IMG_OPACITY_MIN_VALUE,
  IMG_OPACITY_MAX_VALUE
} from "@/constants";
import ImageInput from "@/components/image-input.vue";
import { useBookStore } from "@/stores/book";
import { usePageStore } from "@/stores/web-page";
import { ElementType } from "@/types/book";
import LoadResult from "@/types/load-result-type";

const pageStore = usePageStore();
const bookStore = useBookStore();

const isFabricImageType = (type: ElementType) => {
  return (
    type === ElementType.BG ||
    type === ElementType.GRAPHICS ||
    type === ElementType.IMAGE
  );
};

const _imgSize = ref(IMG_SCALE_MIN_VALUE);

const imgSize = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    let scale = _imgSize.value;
    if (data && isFabricImageType(data.type)) {
      const scaleX = data.scaleX;
      const scaleY = data.scaleY;
      scale = scaleX > scaleY ? scaleX : scaleY;
    }

    return +getLimitedValue(
      scale,
      IMG_SCALE_MIN_VALUE,
      IMG_SCALE_MAX_VALUE
    ).toFixed(1);
  },
  set: (value: number) => {
    value = +value;
    if (Number.isNaN(value)) {
      return;
    }

    value = +getLimitedValue(
      value,
      IMG_SCALE_MIN_VALUE,
      IMG_SCALE_MAX_VALUE
    ).toFixed(1);
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && isFabricImageType(data.type)) {
      bookStore.updateElement(bookStore.currentPageID, data.ID, {
        scaleX: value,
        scaleY: value
      });
    }

    _imgSize.value = value;
  }
});

const _imgOpacity = ref(IMG_OPACITY_MIN_VALUE);
const imgOpacity = computed({
  get: () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    let opacity = _imgOpacity.value;

    if (data && isFabricImageType(data.type)) {
      opacity = data.opacity;
    }
    return +getLimitedValue(
      opacity,
      IMG_OPACITY_MIN_VALUE,
      IMG_OPACITY_MAX_VALUE
    ).toFixed(1);
  },
  set: (value: number) => {
    value = +value;
    if (Number.isNaN(value)) {
      return;
    }

    value = +getLimitedValue(
      value,
      IMG_OPACITY_MIN_VALUE,
      IMG_OPACITY_MAX_VALUE
    ).toFixed(1);

    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (data && isFabricImageType(data.type)) {
      bookStore.updateElement(bookStore.currentPageID, data.ID, {
        opacity: value
      });
    }

    _imgOpacity.value = value;
  }
});

const clip = () => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data || !isFabricImageType(data.type)) {
    return;
  }

  bookStore.setPageClipped(bookStore.currentPageID, data.ID);
};

const imageInputActive = ref(false);
let replacingElementID: string | undefined;
const showImageInputBox = () => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data) {
    return;
  }

  if (data.type !== ElementType.BG && data.type !== ElementType.IMAGE) {
    pageStore.addTips({
      type: "warning",
      text: "只有背景图才能替换"
    });
    return;
  }

  if (replacingElementID) {
    pageStore.addTips({
      type: "warning",
      text: "已有图片正在替换中，请稍后再试"
    });
    return;
  }

  imageInputActive.value = true;
  nextTick(() => {
    imageInputActive.value = false;
  });
};
let tipsLoadingID: string | undefined;
const onUploadImage = () => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data) {
    return;
  }
  replacingElementID = data.ID;
  tipsLoadingID = pageStore.addTips({
    type: "loading",
    text: "正在替换图片",
    duration: -1,
    isOverlay: true
  });
};
const onUploadedImage = (
  data: string | undefined,
  result?: LoadResult | undefined
) => {
  if (!data) {
    switch (result) {
      case LoadResult.IS_LOADING:
        pageStore.addTips({
          type: "warning",
          text: "已有图片正在替换中，请稍后再试"
        });
        break;
      case LoadResult.NO_FILE:
        pageStore.addTips({
          text: "未选择图片"
        });
        break;
      case LoadResult.OVER_MAX_QUANTITY:
        pageStore.addTips({
          type: "warning",
          text: "页面数量超出限制"
        });
        break;
      case LoadResult.OVER_MAX_SIZE:
        pageStore.addTips({
          type: "warning",
          text: "图片大小超出限制"
        });
        break;
      case LoadResult.NOT_IMAGE:
        pageStore.addTips({
          type: "warning",
          text: "非图片文件"
        });
        break;
      case LoadResult.ALL_COMPLETE:
        if (tipsLoadingID) {
          pageStore.removeTips(tipsLoadingID);
          tipsLoadingID = undefined;
        }
        break;
      default:
        pageStore.addTips({
          type: "error",
          text: "图片替换失败，稍后再试试"
        });
        break;
    }
    if (result !== LoadResult.IS_LOADING) {
      replacingElementID = undefined;
    }
    return;
  }
  const pageData = bookStore.getPageSelected(bookStore.currentPageID);
  if (!pageData || pageData.ID !== replacingElementID) {
    replacingElementID = undefined;
    return;
  }

  bookStore.updateElement(bookStore.currentPageID, replacingElementID, {
    src: data
  });
  pageStore.addTips({
    text: "图片替换成功"
  });
  replacingElementID = undefined;
};

const remove = () => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data || !isFabricImageType(data.type)) {
    return;
  }

  bookStore.removeElement(bookStore.currentPageID, data.ID);
};

const rotate = (deg: number) => {
  const data = bookStore.getPageSelected(bookStore.currentPageID);
  if (!data || !isFabricImageType(data.type)) {
    return;
  }

  let angle = (data.angle + deg) / 90;
  if (deg < 0) {
    angle = Math.ceil(angle) * 90;
  } else {
    angle = Math.floor(angle) * 90;
  }
  bookStore.updateElement(bookStore.currentPageID, data.ID, {
    angle: angle
  });
};
</script>

<template>
  <div>
    <div class="image-tool__size">
      <span class="image-tool__size-label"> 大小 </span>
      <v-slider
        class="image-tool__size-slider"
        v-model.number="imgSize"
        :min="IMG_SCALE_MIN_VALUE"
        :max="IMG_SCALE_MAX_VALUE"
        :step="0.1"
        thumb-size="18"
        hide-spin-buttons
        hide-details
      >
        <template v-slot:append>
          <input
            class="image-tool__size-input"
            v-model.number="imgSize"
            type="number"
            step="0.1"
            :min="IMG_SCALE_MIN_VALUE"
            :max="IMG_SCALE_MAX_VALUE"
          />
        </template>
      </v-slider>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div class="image-tool__opacity">
      <span class="image-tool__opacity-label"> 不透明度 </span>
      <v-slider
        class="image-tool__opacity-slider"
        v-model.number="imgOpacity"
        :min="IMG_OPACITY_MIN_VALUE"
        :max="IMG_OPACITY_MAX_VALUE"
        :step="0.1"
        thumb-size="18"
        hide-spin-buttons
        hide-details
      >
        <template v-slot:append>
          <input
            class="image-tool__opacity-input"
            v-model.number="imgOpacity"
            type="number"
            step="0.1"
            :min="IMG_OPACITY_MIN_VALUE"
            :max="IMG_OPACITY_MAX_VALUE"
          />
        </template>
      </v-slider>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div
      class="image-tool__rotate mt-5 d-flex justify-space-around align-center"
    >
      <v-btn
        class="image-tool__rotate-btn"
        variant="plain"
        @click="rotate(-90)"
        stacked
        ><template #prepend>
          <img class="mb-2" src="@/assets/images/rotate-left.png" alt=""
        /></template>
        左旋转
      </v-btn>
      <div class="image-tool__rotate-btn-line"></div>
      <v-btn
        class="image-tool__rotate-btn"
        variant="plain"
        @click="rotate(90)"
        stacked
        ><template #prepend>
          <img class="mb-2" src="@/assets/images/rotate-right.png" alt=""
        /></template>
        右旋转
      </v-btn>
    </div>
    <v-divider class="mt-4"></v-divider>
    <div>
      <v-btn class="image-tool__function-btn" variant="outlined" @click="clip">
        <template #prepend> <img src="@/assets/icons/clip.svg" /></template>
        裁剪
      </v-btn>
      <v-btn
        class="image-tool__function-btn"
        variant="outlined"
        @click="showImageInputBox"
      >
        <template #prepend> <img src="@/assets/icons/replace.svg" /></template>
        替换
      </v-btn>
      <v-btn
        class="image-tool__function-btn"
        variant="outlined"
        @click="remove"
      >
        <template #prepend> <img src="@/assets/icons/delete.svg" /></template>
        删除
      </v-btn>
    </div>

    <image-input
      :active="imageInputActive"
      @update:upload="onUploadImage"
      @update:uploaded="onUploadedImage"
    ></image-input>
  </div>
</template>

<style>
.image-tool__size-label,
.image-tool__opacity-label {
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
}

.image-tool__size-slider,
.image-tool__opacity-slider {
  margin: 0 !important;
}

.image-tool__size-input,
.image-tool__opacity-input {
  border: 0.3px solid rgb(var(--v-theme-on-surface-lighten-1-input-border));
  padding: 0 0.5rem;
  width: 3.75rem;
  height: 1.75rem;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-lighten-1));
  background: rgb(var(--v-theme-on-surface-lighten-1-input));
  outline: 0;
  box-shadow:
    inset 0 1px 2px 0
      rgb(var(--v-theme-on-surface-lighten-1-input-border-shadow)),
    inset 0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-active-2));
}

.image-tool__opacity {
  padding-top: 1.5rem;
}

.image-tool__rotate .image-tool__rotate-btn {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-darken-2));
}

.image-tool__rotate-btn-line {
  width: 1px;
  height: 50px;
  background: rgb(var(--v-theme-on-surface-lighten-1-divider));
}

.image-tool__function-btn {
  margin-top: 1.5rem;
  border: 0;
  border-radius: 10px;
  width: 100%;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: 0;
  color: rgb(var(--v-theme-on-surface-darken-3));
  background: rgb(var(--v-theme-on-surface-lighten-1-active-2));
  box-shadow:
    0 0 1px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow)),
    inset 0 0 0 0
      rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-inset)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1)),
    0 0 2px 0 rgb(var(--v-theme-on-surface-lighten-1-btn-active-shadow-1));
}
</style>
