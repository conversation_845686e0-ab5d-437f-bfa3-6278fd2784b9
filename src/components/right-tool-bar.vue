<!--
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-29 16:36:26
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-01-20 15:11:03
 * @Description:
-->
<script setup lang="ts">
import { computed, ref, watch } from "vue";
import textTool from "./text-tool.vue";
import imageTool from "./image-tool.vue";
import { useBookStore } from "@/stores/book";
import { usePageStore } from "@/stores/web-page";
import { ElementType } from "@/types/book";
import getLimitedValue from "@/utils/get-limited-value";
import {
  CANVAS_MAX_SCALE,
  CANVAS_MIN_SCALE,
  CANVAS_SCALE_STEP
} from "@/constants";

const bookStore = useBookStore();
const pageStore = usePageStore();

const tab = ref("text");

watch(
  () => {
    return bookStore.pages[bookStore.currentPageIndexInArray].selected;
  },
  () => {
    const data = bookStore.getPageSelected(bookStore.currentPageID);
    if (
      data?.type === ElementType.GRAPHICS ||
      data?.type === ElementType.BG ||
      data?.type === ElementType.IMAGE
    ) {
      tab.value = "img";
    } else if (data?.type === ElementType.TEXTBOX) {
      tab.value = "text";
    }
  }
);

const canvasScale = computed({
  get: () => {
    return pageStore.canvasScale;
  },
  set: (offset: number) => {
    offset = +offset;
    if (Number.isNaN(offset)) {
      return;
    }

    pageStore.updateCanvasScale(
      getLimitedValue(offset, CANVAS_MIN_SCALE, CANVAS_MAX_SCALE)
    );
  }
});

const zoom = (offset: number) => {
  pageStore.updateCanvasScale(
    +getLimitedValue(
      canvasScale.value + offset,
      CANVAS_MIN_SCALE,
      CANVAS_MAX_SCALE
    ).toFixed(1)
  );
};
</script>

<template>
  <v-navigation-drawer
    class="right-tool-bar"
    location="right"
    permanent
    touchless
    width="257"
  >
    <v-tabs class="right-tool-bar__tabs mt-1 ps-1 pe-1" v-model="tab" grow>
      <v-tab
        class="right-tool-bar__tab-btn"
        selected-class="right-tool-bar__tab-btn--active"
        :hide-slider="true"
        value="text"
        >文字</v-tab
      >
      <v-tab
        class="right-tool-bar__tab-btn"
        selected-class="right-tool-bar__tab-btn--active"
        :hide-slider="true"
        value="img"
        >图像</v-tab
      >
    </v-tabs>
    <v-divider class="mt-2"></v-divider>
    <v-tabs-window class="pt-5 ps-5 pe-5" v-model="tab">
      <v-tabs-window-item value="text">
        <text-tool></text-tool>
      </v-tabs-window-item>

      <v-tabs-window-item value="img">
        <image-tool></image-tool>
      </v-tabs-window-item>
    </v-tabs-window>
    <div class="right-tool-bar__scale">
      <v-slider
        class="right-tool-bar__scale-slider"
        v-model.number="canvasScale"
        :min="CANVAS_MIN_SCALE"
        :max="CANVAS_MAX_SCALE"
        :step="CANVAS_SCALE_STEP"
        thumb-size="18"
        hide-spin-buttons
        hide-details
        ><template #prepend>
          <span
            class="right-tool-bar__scale-slider-btn"
            @click="zoom(-CANVAS_SCALE_STEP)"
          >
            -
          </span>
        </template>
        <template #append>
          <span
            class="right-tool-bar__scale-slider-btn"
            @click="zoom(CANVAS_SCALE_STEP)"
          >
            +
          </span>
        </template>
      </v-slider>
    </div>
  </v-navigation-drawer>
</template>

<style>
.right-tool-bar {
  margin-top: 2px;
  padding-bottom: 3rem;
  background: rgb(var(--v-theme-surface-lighten-1)) !important;
}

.right-tool-bar .right-tool-bar__tabs .right-tool-bar__tab-btn {
  border-radius: 10px !important;
  font-size: 1rem;
  color: rgb(var(--v-theme-on-surface-lighten-1));
}

.right-tool-bar .right-tool-bar__tabs .right-tool-bar__tab-btn--active {
  background: rgb(var(--v-theme-on-surface-lighten-1-active));
}

.right-tool-bar__scale {
  position: absolute;
  bottom: 0;
  border-top: 1px solid #000;
  padding: 5px 20px 0;
  width: 100%;
  height: 3rem;
}

.right-tool-bar__scale-slider {
  margin: 0 !important;
}

.right-tool-bar__scale-slider-btn {
  font-size: 1.5rem;
  color: #838383;
  cursor: pointer;
  user-select: none;
}
</style>
