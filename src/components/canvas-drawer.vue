<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-11-11 14:22:48
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2025-06-25 17:07:24
 * @Description:
-->
<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  type WatchHandle,
  type Ref,
  nextTick
} from "vue";
import {
  FabricObject,
  FabricImage,
  Rect,
  Textbox,
  Group,
  type TEvent,
  type TPointerEvent
} from "fabric";

import { CustomFabricCanvas } from "@/custom-fabric/index";
import {
  THUMBNAIL_WIDTH,
  CANVAS_BG_COLOR,
  CLIP_BOX_COLOR,
  CLIP_BOX_DASH,
  CLIP_BOX_MASKER_OPACITY,
  IMG_SCALE_MIN_VALUE,
  IMG_SCALE_MAX_VALUE,
  BOOK_PAGE_MAX_QUANTITY,
  CANVAS_INPUT_PLACEHOLDER,
  TEXT_INIT_WIDTH
} from "@/constants";
import {
  useBookStore,
  onBeforeSwitchPage,
  executeBeforeSwitchPageCallbacks
} from "@/stores/book";
import { usePageStore, ExportType } from "@/stores/web-page";
import CLIP_CANCEL_BTN from "@/assets/images/clip-cancel.png";
import CLIP_CONFIRM_BTN from "@/assets/images/clip-confirm.png";
import UPLOAD_BTN_PNG from "@/assets/images/upload-img-2.png";
import { lessThan, greaterThan } from "@/utils/fuzzy-compare";
import setClipCorner from "@/utils/set-clip-cornor";
import getRelativeCoord from "@/utils/get-relative-coord";
import getLimitedValue from "@/utils/get-limited-value";
import { generatePDF, generatePNG } from "@/utils/generate-pdf-png";
import {
  drawFabricImage,
  drawFabricTextbox,
  drawUploadBgImgBtn,
  scaleElementToFit
} from "@/utils/canvas-draw-object";
import checkNonInteractiveArea from "@/utils/check-non-interactive-area";
import limitElementInCanvas from "@/utils/limit-element-in-canvas";
import ImageInput from "./image-input.vue";
import LoadResult from "@/types/load-result-type";
import getDataUrlSize from "@/utils/get-data-url-size";

const pageStore = usePageStore();
const bookStore = useBookStore();

let canvas: CustomFabricCanvas | undefined;
const resetCanvas = () => {
  removeAllFabricObjects();
  removeAllWatchHandles();
  if (!canvas) {
    return;
  }

  canvas.clear();
  canvas.backgroundColor = CANVAS_BG_COLOR;
  canvas.requestRenderAll();
};
const removeObjectFromCanvas = (obj: FabricObject) => {
  if (!canvas) {
    return;
  }

  canvas.remove(obj);
  canvas.requestRenderAll();
};

// 记录所有的fabric对象
const allFabricObjects: { [key: string]: FabricObject } = {};
const addFabricObject = (ID: string, obj: FabricObject) => {
  allFabricObjects[ID] = obj;
};
// 重置
const removeAllFabricObjects = () => {
  const keys = Object.keys(allFabricObjects);
  for (const key of keys) {
    delete allFabricObjects[key];
  }
};
const removeFabricObject = (ID: string) => {
  if (!(ID in allFabricObjects)) {
    return;
  }

  const obj = allFabricObjects[ID];
  removeWatchHandle(ID);
  removeObjectFromCanvas(obj);
  delete allFabricObjects[ID];
};
// 删除不存在的 fabric 对象
const removeNotExistFabricObjects = () => {
  const keys = Object.keys(allFabricObjects);
  for (const key of keys) {
    const obj = allFabricObjects[key];

    if (
      !bookStore.checkElementExist(bookStore.currentPageID, obj.get("__ID"))
    ) {
      removeObjectFromCanvas(obj);
      removeWatchHandle(key);
      delete allFabricObjects[key];
    }
  }
};

// 记录非顶层的 watch 对象
const watchHandles: { [key: string]: WatchHandle } = {};
const addWatchHandle = (key: string, watcher: WatchHandle) => {
  watchHandles[key] = watcher;
};
const _removeWatchHandle = (key: string) => {
  watchHandles[key]();
  delete watchHandles[key];
};
const removeWatchHandle = (key: string) => {
  if (key in watchHandles) {
    _removeWatchHandle(key);
  }
};
const removeAllWatchHandles = () => {
  const keys = Object.keys(watchHandles);
  for (const key of keys) {
    _removeWatchHandle(key);
  }
};

// 切换书本页
watch(
  () => {
    return bookStore.currentPageID;
  },
  (newVal, oldVal) => {
    if (!canvas) {
      return;
    }

    executeBeforeSwitchPageCallbacks(oldVal);

    if (!canvas.isEmpty()) {
      const thumbnail = canvas.toDataURL({
        multiplier: THUMBNAIL_WIDTH / canvas.width
      });
      if (thumbnail) {
        bookStore.setPageThumbnail(oldVal, thumbnail);
      } else {
        bookStore.setPageThumbnail(oldVal, undefined);
      }
    } else {
      bookStore.setPageThumbnail(oldVal, undefined);
    }

    resetCanvas();
  }
);

let bgChangedFrequency = 0;
watch(
  () => {
    return bookStore.pages[bookStore.currentPageIndexInArray].bg;
  },
  async (newVal, oldVal) => {
    bgChangedFrequency++;
    const currentBgChangedFrequency = bgChangedFrequency;
    if (!canvas) {
      return;
    }

    if (oldVal) {
      removeFabricObject(oldVal.ID);
    }

    if (!newVal) {
      return;
    }

    if (!(newVal.ID in allFabricObjects)) {
      const oImg = await drawFabricImage(canvas, newVal);
      if (!canvas) {
        return;
      }
      if (
        !(oImg instanceof FabricImage) ||
        currentBgChangedFrequency !== bgChangedFrequency
      ) {
        canvas.remove(oImg as FabricObject);
        return;
      }

      if (newVal.shouldScaleToFitContainer) {
        scaleElementToFit(oImg, canvas.width, canvas.height);
      }

      bookStore.updatePageBG(
        newVal.belongToPage,
        {
          width: oImg.width,
          height: oImg.height,
          scaleX: oImg.scaleX,
          scaleY: oImg.scaleY,
          shouldScaleToFitContainer: false
        },
        true
      );
      canvas.requestRenderAll();

      const watchHandle = watch(newVal, async (newVal) => {
        if (!canvas || newVal.objHasModified) {
          return;
        }

        const obj = allFabricObjects[newVal.ID];
        if (!obj) {
          return;
        }

        obj
          .set({
            left: newVal.left,
            top: newVal.top,
            width: newVal.width,
            height: newVal.height,
            scaleX: newVal.scaleX,
            scaleY: newVal.scaleY,
            opacity: newVal.opacity
          })
          .rotate(newVal.angle);

        limitElementInCanvas(obj as FabricImage, canvas.width, canvas.height);
        canvas.requestRenderAll();
        bookStore.updatePageBG(
          bookStore.currentPageID,
          {
            left: obj.left,
            top: obj.top
          },
          true
        );

        if (newVal.src !== (obj as FabricImage).getSrc()) {
          try {
            await (obj as FabricImage).setSrc(newVal.src);
          } catch {
            bookStore.updatePageBG(
              newVal.belongToPage,
              {
                src: (obj as FabricImage).getSrc()
              },
              true
            );
            return;
          }

          if (!canvas || !(obj.get("__ID") in allFabricObjects)) {
            return;
          }

          scaleElementToFit(obj as FabricImage, canvas.width, canvas.height);
          limitElementInCanvas(obj as FabricImage, canvas.width, canvas.height);
          canvas.requestRenderAll();

          bookStore.updatePageBG(
            newVal.belongToPage,
            {
              left: obj.left,
              top: obj.top,
              width: obj.width,
              height: obj.height,
              scaleX: obj.scaleX,
              scaleY: obj.scaleY
            },
            true
          );
        }
      });

      addWatchHandle(newVal.ID, watchHandle);
      addFabricObject(newVal.ID, oImg);
    }
  }
);

let textChangedFrequency = 0;
// 设置文字时
watch(
  [
    () => {
      return Object.keys(
        bookStore.pages[bookStore.currentPageIndexInArray].texts
      ).length;
    },
    () => {
      // 防止切换页面时，两个页面的文本数量相同，导致不触发 watch
      return bookStore.pages[bookStore.currentPageIndexInArray].texts;
    }
  ],
  async () => {
    textChangedFrequency++;
    const currentTextChangedFrequency = textChangedFrequency;
    if (!canvas) {
      return;
    }

    removeNotExistFabricObjects();

    const page = bookStore.getPage(bookStore.currentPageID);
    if (!page) {
      return;
    }

    const texts = page.texts;

    for (const key in texts) {
      const t = texts[key];

      const obj = allFabricObjects[t.ID];
      if (!obj) {
        const obj = await drawFabricTextbox(canvas, t);
        if (!canvas) {
          return;
        }

        if (
          !(obj instanceof Textbox) ||
          currentTextChangedFrequency !== textChangedFrequency
        ) {
          canvas.remove(obj as FabricObject);
          return;
        }

        const watcher = watch(t, (newVal) => {
          if (!canvas || newVal.objHasModified) {
            return;
          }

          const obj = allFabricObjects[newVal.ID];
          if ((obj as Textbox).isEditing) {
            (obj as Textbox).exitEditing();
          }
          obj
            .set({
              left: newVal.left,
              top: newVal.top,
              width: newVal.width,
              height: newVal.height,
              scaleX: newVal.scaleX,
              scaleY: newVal.scaleY,
              opacity: newVal.opacity,
              fontSize: newVal.fontSize,
              fontFamily: newVal.fontFamily,
              fill: newVal.color
            })
            .rotate(newVal.angle);
          canvas.moveObjectTo(obj, obj.get("depth"));
          limitElementInCanvas(obj as Textbox, canvas.width, canvas.height);
          canvas.requestRenderAll();

          bookStore.updatePageText(
            newVal.belongToPage,
            newVal.ID,
            {
              left: obj.left,
              top: obj.top,
              width: obj.width
            },
            true
          );
        });

        addWatchHandle(t.ID, watcher);
        addFabricObject(t.ID, obj);
      }
    }
  }
);

let graphicsChangedFrequency = 0;
// 设置图形时
watch(
  [
    () => {
      return Object.keys(
        bookStore.pages[bookStore.currentPageIndexInArray].graphics
      ).length;
    },
    () => {
      return bookStore.pages[bookStore.currentPageIndexInArray].graphics;
    }
  ],
  async () => {
    graphicsChangedFrequency++;
    const currentGraphicsChangedFrequency = graphicsChangedFrequency;
    if (!canvas) {
      return;
    }

    removeNotExistFabricObjects();

    const page = bookStore.getPage(bookStore.currentPageID);
    if (!page) {
      return;
    }

    const graphics = { ...page.graphics };

    for (const key in graphics) {
      const g = graphics[key];

      const obj = allFabricObjects[g.ID];
      if (!obj) {
        let tipsLoadingID: string | undefined;
        tipsLoadingID = pageStore.addTips({
          type: "loading",
          text: "正在加载图形",
          duration: -1,
          isOverlay: true
        });
        const oImg = await drawFabricImage(canvas, g);
        if (tipsLoadingID) {
          pageStore.removeTips(tipsLoadingID);
        }

        if (!canvas) {
          return;
        }
        if (currentGraphicsChangedFrequency !== graphicsChangedFrequency) {
          canvas.remove(oImg as FabricImage);
          return;
        }

        if (!(oImg instanceof FabricImage)) {
          tipsLoadingID = pageStore.addTips({
            type: "error",
            text: "加载图形失败，稍后再试试"
          });

          bookStore.removePageGraphics(g.belongToPage, g.ID);

          return;
        }

        bookStore.updatePageGraphics(
          g.belongToPage,
          g.ID,
          {
            width: oImg.width,
            height: oImg.height
          },
          true
        );

        const watchHandle = watch(g, async (newVal) => {
          if (!canvas || newVal.objHasModified) {
            return;
          }

          const obj = allFabricObjects[newVal.ID];
          obj
            .set({
              left: newVal.left,
              top: newVal.top,
              width: newVal.width,
              height: newVal.height,
              scaleX: newVal.scaleX,
              scaleY: newVal.scaleY,
              opacity: newVal.opacity
            })
            .rotate(newVal.angle);

          canvas.moveObjectTo(obj, obj.get("depth"));

          limitElementInCanvas(obj as FabricImage, canvas.width, canvas.height);
          canvas.requestRenderAll();
          bookStore.updatePageGraphics(
            g.belongToPage,
            newVal.ID,
            {
              left: obj.left,
              top: obj.top
            },
            true
          );

          if (newVal.src !== (obj as FabricImage).getSrc()) {
            try {
              await (obj as FabricImage).setSrc(newVal.src);
            } catch {
              bookStore.updatePageGraphics(
                newVal.belongToPage,
                newVal.ID,
                {
                  src: (obj as FabricImage).getSrc()
                },
                true
              );
              return;
            }

            if (!canvas || !(newVal.ID in allFabricObjects)) {
              return;
            }
            limitElementInCanvas(
              obj as FabricImage,
              canvas.width,
              canvas.height
            );
            canvas.requestRenderAll();

            bookStore.updatePageGraphics(
              newVal.belongToPage,
              newVal.ID,
              {
                left: obj.left,
                top: obj.top,
                width: obj.width,
                height: obj.height
              },
              true
            );
          }
        });

        addWatchHandle(g.ID, watchHandle);
        addFabricObject(g.ID, oImg);
      }
    }
  }
);

let imageChangedFrequency = 0;
// 设置图片时
watch(
  [
    () => {
      return Object.keys(
        bookStore.pages[bookStore.currentPageIndexInArray].images
      ).length;
    },
    () => {
      return bookStore.pages[bookStore.currentPageIndexInArray].images;
    }
  ],
  async () => {
    imageChangedFrequency++;
    const currentImageChangedFrequency = imageChangedFrequency;

    if (!canvas) {
      return;
    }

    removeNotExistFabricObjects();

    const page = bookStore.getPage(bookStore.currentPageID);
    if (!page) {
      return;
    }

    const images = { ...page.images };

    for (const key in images) {
      const config = images[key];

      const obj = allFabricObjects[config.ID];
      if (!obj) {
        const oImg = await drawFabricImage(canvas, config);
        if (!canvas) {
          return;
        }
        if (currentImageChangedFrequency !== imageChangedFrequency) {
          canvas.remove(oImg as FabricImage);
          return;
        }
        if (!(oImg instanceof FabricImage)) {
          pageStore.addTips({
            type: "error",
            text: "加载图片失败，稍后再试试"
          });

          continue;
        }

        if (config.shouldScaleToFitContainer) {
          scaleElementToFit(oImg, canvas.width, canvas.height);
        }
        bookStore.updatePageImage(
          config.belongToPage,
          config.ID,
          {
            width: oImg.width,
            height: oImg.height,
            scaleX: oImg.scaleX,
            scaleY: oImg.scaleY,
            shouldScaleToFitContainer: false
          },
          true
        );
        canvas.requestRenderAll();

        const watchHandle = watch(config, async (newVal) => {
          if (!canvas || newVal.objHasModified) {
            return;
          }

          const obj = allFabricObjects[newVal.ID];
          obj
            .set({
              left: newVal.left,
              top: newVal.top,
              width: newVal.width,
              height: newVal.height,
              scaleX: newVal.scaleX,
              scaleY: newVal.scaleY,
              opacity: newVal.opacity
            })
            .rotate(newVal.angle);

          canvas.moveObjectTo(obj, obj.get("depth"));

          limitElementInCanvas(obj as FabricImage, canvas.width, canvas.height);
          canvas.requestRenderAll();
          bookStore.updatePageImage(
            config.belongToPage,
            newVal.ID,
            {
              left: obj.left,
              top: obj.top
            },
            true
          );

          if (newVal.src !== (obj as FabricImage).getSrc()) {
            try {
              await (obj as FabricImage).setSrc(newVal.src);
            } catch {
              bookStore.updatePageImage(
                newVal.belongToPage,
                newVal.ID,
                {
                  src: (obj as FabricImage).getSrc()
                },
                true
              );
              return;
            }

            if (!canvas || !(newVal.ID in allFabricObjects)) {
              return;
            }

            scaleElementToFit(obj as FabricImage, canvas.width, canvas.height);
            limitElementInCanvas(
              obj as FabricImage,
              canvas.width,
              canvas.height
            );
            canvas.requestRenderAll();

            bookStore.updatePageImage(
              newVal.belongToPage,
              newVal.ID,
              {
                left: obj.left,
                top: obj.top,
                width: obj.width,
                height: obj.height,
                scaleX: obj.scaleX,
                scaleY: obj.scaleY
              },
              true
            );
          }
        });

        addWatchHandle(config.ID, watchHandle);
        addFabricObject(config.ID, oImg);
      }
    }
  }
);

const imageInputActive = ref(false);
const imageInputQuantity = 1;
const showImageInputBox = () => {
  imageInputActive.value = true;
  nextTick(() => {
    imageInputActive.value = false;
  });
};
let imageTipsLoadingID: string | undefined;
const onUploadImage = () => {
  imageTipsLoadingID = pageStore.addTips({
    type: "loading",
    text: "正在加载图片",
    duration: -1,
    isOverlay: true
  });
};
const imageData = ref("");
const onUploadedImage = (
  data: string | undefined,
  result?: LoadResult | undefined
) => {
  if (!data) {
    switch (result) {
      case LoadResult.IS_LOADING:
        pageStore.addTips({
          type: "warning",
          text: "已有图片正在加载中，请稍后再试"
        });
        break;
      case LoadResult.NO_FILE:
        pageStore.addTips({
          text: "未选择图片"
        });
        break;
      case LoadResult.OVER_MAX_QUANTITY:
        pageStore.addTips({
          type: "warning",
          text: "图片数量超出限制"
        });
        break;
      case LoadResult.OVER_MAX_SIZE:
        pageStore.addTips({
          type: "warning",
          text: "图片大小超出限制"
        });
        break;
      case LoadResult.NOT_IMAGE:
        pageStore.addTips({
          type: "warning",
          text: "非图片文件"
        });
        break;
      case LoadResult.ALL_COMPLETE:
        if (imageTipsLoadingID) {
          pageStore.removeTips(imageTipsLoadingID);
          imageTipsLoadingID = undefined;
        }

        break;
      default:
        pageStore.addTips({
          type: "error",
          text: "图片加载失败，稍后再试试"
        });
        break;
    }
    return;
  }

  pageStore.addTips({
    text: "已添加图片"
  });
  imageData.value = data;
};

let imgUploadBtnChangedFrequency = 0;
// 设置图片上传按钮时
watch(
  [
    () => {
      return bookStore.pages[bookStore.currentPageIndexInArray].imgUploadBtns;
    }
  ],
  async () => {
    imgUploadBtnChangedFrequency++;
    const currentImgUploadBtnChangedFrequency = imgUploadBtnChangedFrequency;

    if (!canvas) {
      return;
    }

    removeNotExistFabricObjects();

    const page = bookStore.getPage(bookStore.currentPageID);
    if (!page) {
      return;
    }

    const imgUploadBtns = { ...page.imgUploadBtns };

    for (const key in imgUploadBtns) {
      const config = imgUploadBtns[key];

      const obj = allFabricObjects[config.ID];
      if (!obj) {
        const btn = await drawFabricImage(canvas, {
          ...config,
          src: UPLOAD_BTN_PNG,
          shouldScaleToFitContainer: false
        });
        if (!canvas) {
          return;
        }
        if (
          currentImgUploadBtnChangedFrequency !== imgUploadBtnChangedFrequency
        ) {
          canvas.remove(btn as FabricImage);
          return;
        }
        if (!(btn instanceof FabricImage)) {
          pageStore.addTips({
            type: "error",
            text: "加载上传失败，稍后再试试"
          });

          continue;
        }

        btn.set({
          selectable: false,
          hoverCursor: "pointer"
        });
        btn.on("mousedown", showImageInputBox);
        const watchHandle = watch(imageData, async (newVal) => {
          if (!canvas || !newVal) {
            return;
          }

          const { width, height } = await getDataUrlSize(newVal);
          if (!canvas) {
            return;
          }

          if (
            currentImgUploadBtnChangedFrequency !== imgUploadBtnChangedFrequency
          ) {
            canvas.remove(btn as FabricImage);
            return;
          }

          if (width !== -1 && height !== -1) {
            bookStore.addPageImage(bookStore.currentPageID, {
              src: newVal,
              left: config.left,
              top: config.top,
              scaleX: btn.getScaledWidth() / width,
              scaleY: btn.getScaledHeight() / height
            });
          } else {
            bookStore.addPageImage(bookStore.currentPageID, {
              src: newVal,
              left: config.left,
              top: config.top
            });
          }
        });

        addWatchHandle(config.ID, watchHandle);
        addFabricObject(config.ID, btn);
      }
    }
  }
);

// 设置prePage 时
watch(bookStore.prePages, async (newVal) => {
  if (!canvas) {
    return;
  }

  if (newVal.length === 0) {
    return;
  }

  newVal = [...newVal];

  bookStore.removeAllPrePage();

  let bgObj: Rect | undefined = new Rect({
    left: 0,
    top: 0,
    strokeWidth: 0,
    width: bookStore.width,
    height: bookStore.height,
    fill: CANVAS_BG_COLOR
  });

  let group: Group | undefined = new Group([bgObj]);

  for (const page of newVal) {
    if (page.bg) {
      const bgConfig = page.bg;
      const oImg = await drawFabricImage(group, bgConfig);

      if (
        !canvas ||
        !(oImg instanceof FabricImage) ||
        bookStore.pages.length >= BOOK_PAGE_MAX_QUANTITY
      ) {
        bgObj = undefined;
        group.removeAll();
        group = undefined;
        return;
      }

      scaleElementToFit(oImg, bgObj.width, bgObj.height);

      bgConfig.width = oImg.width;
      bgConfig.height = oImg.height;
      bgConfig.scaleX = oImg.scaleX;
      bgConfig.scaleY = oImg.scaleY;

      if (group.width !== bgObj.width || group.height !== bgObj.height) {
        group.remove(oImg);
        group.add(oImg);
      }

      page.thumbnail = group.toDataURL({
        multiplier: THUMBNAIL_WIDTH / bgObj.width
      });

      bookStore.addPageByConfig(page);

      group.removeAll();
      group.add(bgObj);
    }
  }
});

// 裁剪
interface IClipHelperObj {
  cloneObj: FabricImage | undefined;
  clipBox: Rect | undefined;
  confirmBtn: FabricImage | undefined;
  cancelBtn: FabricImage | undefined;
}
let clippedObj: FabricImage | undefined = undefined;
const clipHelperObj: IClipHelperObj = {
  cloneObj: undefined,
  clipBox: undefined,
  confirmBtn: undefined,
  cancelBtn: undefined
};
const cancelClip = () => {
  resetClipHelper();

  if (clippedObj) {
    clippedObj.set("opacity", clippedObj.get("_originalOpacity"));
    clippedObj = undefined;
  }

  canvas?.requestRenderAll();
};
const cancelClipByKey = (event: KeyboardEvent) => {
  if (event.key === "Escape" && clippedObj) {
    cancelClip();
    bookStore.setPageClipped(bookStore.currentPageID, undefined);
  }
};
const resetClipHelper = () => {
  for (const key in clipHelperObj) {
    canvas?.remove(clipHelperObj[key as keyof IClipHelperObj] as FabricObject);
    clipHelperObj[key as keyof IClipHelperObj] = undefined;
  }
};
const checkClipHelperObj = (obj: FabricObject) => {
  for (const key in clipHelperObj) {
    if (clipHelperObj[key as keyof IClipHelperObj] === obj) {
      return true;
    }
  }

  return false;
};
onBeforeSwitchPage((pageID) => {
  cancelClip();
  bookStore.setPageClipped(pageID, undefined);
});
let clipFrequency = 0;
watch(
  () => {
    return bookStore.pages[bookStore.currentPageIndexInArray].clipped;
  },
  async (newID) => {
    clipFrequency++;
    const currentClipFrequency = clipFrequency;

    cancelClip();

    if (!newID || !canvas) {
      return;
    }

    const obj = allFabricObjects[newID];
    if (!obj || !(obj instanceof FabricImage)) {
      bookStore.setPageClipped(bookStore.currentPageID, undefined);
      return;
    }

    clippedObj = obj;

    let cloneObj: FabricImage | undefined = undefined;
    try {
      cloneObj = await obj.clone();
    } catch {
      if (currentClipFrequency === clipFrequency) {
        bookStore.setPageClipped(bookStore.currentPageID, undefined);
        clippedObj = undefined;
      }
      cloneObj = undefined;

      return;
    }

    if (!canvas || currentClipFrequency !== clipFrequency) {
      cloneObj = undefined;
      return;
    }

    canvas.discardActiveObject();

    obj.set({
      _originalOpacity: obj.opacity,
      opacity: obj.opacity * CLIP_BOX_MASKER_OPACITY
    });

    cloneObj.set({
      depth: obj.get("depth"),
      selectable: false,
      evented: false
    });
    const clipPath = new Rect({
      left: -cloneObj.width / 2,
      top: -cloneObj.height / 2,
      width: cloneObj.width,
      height: cloneObj.height,
      strokeWidth: 0,
      seletable: false,
      evented: false
    });
    cloneObj.clipPath = clipPath;
    clipHelperObj.cloneObj = cloneObj;
    canvas.add(cloneObj);

    FabricImage.fromURL(CLIP_CANCEL_BTN)
      .then((oImg) => {
        if (!canvas || currentClipFrequency !== clipFrequency) {
          return;
        }

        const { left, top, width, height } = clipBox.getBoundingRect();
        oImg.set({
          left: left + width / 2 - oImg.width - 20,
          top: top + height + 10,
          selectable: false,
          hoverCursor: "pointer"
        });
        oImg.on("mousedown", () => {
          cancelClip();
          bookStore.setPageClipped(bookStore.currentPageID, undefined);
        });

        clipHelperObj.cancelBtn = oImg;
        canvas.add(oImg);
        canvas.bringObjectToFront(oImg);
        canvas.requestRenderAll();
      })
      .catch(() => {});
    FabricImage.fromURL(CLIP_CONFIRM_BTN)
      .then((oImg) => {
        if (!canvas || currentClipFrequency !== clipFrequency) {
          return;
        }

        const { left, top, width, height } = clipBox.getBoundingRect();
        oImg.set({
          left: left + width / 2 + 20,
          top: top + height + 10,
          selectable: false,
          hoverCursor: "pointer"
        });
        oImg.on("mousedown", () => {
          if (
            lessThan(clipBox.getScaledWidth(), cloneObj.getScaledWidth()) ||
            lessThan(clipBox.getScaledHeight(), cloneObj.getScaledHeight())
          ) {
            bookStore.updateElement(bookStore.currentPageID, obj.get("__ID"), {
              left: clipBox.left,
              top: clipBox.top,
              scaleX: 1,
              scaleY: 1,
              src: cloneObj
                .set({
                  angle: 0,
                  opacity: 1
                })
                .toDataURL({
                  left: (clipPath.left + cloneObj.width / 2) * cloneObj.scaleX,
                  top: (clipPath.top + cloneObj.height / 2) * cloneObj.scaleY,
                  width: clipBox.getScaledWidth(),
                  height: clipBox.getScaledHeight()
                })
            });
          }

          cancelClip();
          bookStore.setPageClipped(bookStore.currentPageID, undefined);
        });

        clipHelperObj.confirmBtn = oImg;
        canvas.add(oImg);
        canvas.bringObjectToFront(oImg);
        canvas.requestRenderAll();
      })
      .catch(() => {});

    const clipBox = new Rect({
      left: cloneObj.left,
      top: cloneObj.top,
      width: cloneObj.getScaledWidth(),
      height: cloneObj.getScaledHeight(),
      depth: obj.get("depth"),
      angle: cloneObj.angle,
      fill: "transparent",
      lockRotation: true,
      strokeWidth: 0,
      borderDashArray: CLIP_BOX_DASH,
      borderColor: CLIP_BOX_COLOR,
      cornerStrokeColor: CLIP_BOX_COLOR
    });

    clipBox.setControlsVisibility({
      mtr: false
    });
    setClipCorner(clipBox);

    const setClippedArea = () => {
      if (!canvas) {
        return;
      }

      const [clipBoxRelativeX, clipBoxRelativeY] = getRelativeCoord(
        clipBox.left,
        clipBox.top,
        clipBox.get("angle")
      );
      const [cloneObjRelativeX, cloneObjRelativeY] = getRelativeCoord(
        cloneObj.left,
        cloneObj.top,
        cloneObj.get("angle")
      );

      clipPath.set({
        left:
          (clipBoxRelativeX - cloneObjRelativeX) / cloneObj.scaleX -
          cloneObj.width / 2,
        top:
          (clipBoxRelativeY - cloneObjRelativeY) / cloneObj.scaleY -
          cloneObj.height / 2,
        width: clipBox.getScaledWidth() / cloneObj.scaleX,
        height: clipBox.getScaledHeight() / cloneObj.scaleY
      });

      cloneObj.clipPath = undefined;
      canvas.renderAll();
      cloneObj.clipPath = clipPath;
      canvas.requestRenderAll();
    };

    // 限制剪切框缩放范围
    clipBox.on("scaling", () => {
      const scaledWidth = clipBox.getScaledWidth();
      const scaledHeight = clipBox.getScaledHeight();

      const originalScaledWidth = obj.getScaledWidth();
      const originalScaledHeight = obj.getScaledHeight();

      const [clipBoxRelativeLeft, clipBoxRelativeTop] = getRelativeCoord(
        clipBox.left,
        clipBox.top,
        clipBox.get("angle")
      );
      const [originalObjRelativeLeft, originalObjRelativeTop] =
        getRelativeCoord(cloneObj.left, cloneObj.top, cloneObj.get("angle"));

      let clipBoxLeft = clipBoxRelativeLeft;
      let clipBoxTop = clipBoxRelativeTop;
      if (
        greaterThan(
          clipBoxRelativeLeft + scaledWidth,
          originalObjRelativeLeft + originalScaledWidth
        )
      ) {
        clipBox.set(
          "scaleX",
          (originalObjRelativeLeft +
            originalScaledWidth -
            clipBoxRelativeLeft) /
            clipBox.width
        );
      } else if (lessThan(clipBoxRelativeLeft, originalObjRelativeLeft)) {
        clipBox.set(
          "scaleX",
          (clipBoxRelativeLeft + scaledWidth - originalObjRelativeLeft) /
            clipBox.width
        );
        clipBoxLeft = originalObjRelativeLeft;
      }

      if (
        greaterThan(
          clipBoxRelativeTop + scaledHeight,
          originalObjRelativeTop + originalScaledHeight
        )
      ) {
        clipBox.set(
          "scaleY",
          (originalObjRelativeTop + originalScaledHeight - clipBoxRelativeTop) /
            clipBox.height
        );
      } else if (lessThan(clipBoxRelativeTop, originalObjRelativeTop)) {
        clipBox.set(
          "scaleY",
          (clipBoxRelativeTop + scaledHeight - originalObjRelativeTop) /
            clipBox.height
        );

        clipBoxTop = originalObjRelativeTop;
      }
      [clipBoxLeft, clipBoxTop] = getRelativeCoord(
        clipBoxLeft,
        clipBoxTop,
        -clipBox.get("angle")
      );
      clipBox.set({
        left: clipBoxLeft,
        top: clipBoxTop
      });

      setClippedArea();
    });

    clipBox.on("moving", () => {
      const scaledWidth = clipBox.getScaledWidth();
      const scaledHeight = clipBox.getScaledHeight();

      const originalScaledWidth = obj.getScaledWidth();
      const originalScaledHeight = obj.getScaledHeight();

      const [clipBoxRelativeLeft, clipBoxRelativeTop] = getRelativeCoord(
        clipBox.left,
        clipBox.top,
        clipBox.get("angle")
      );
      const [originalObjRelativeLeft, originalObjRelativeTop] =
        getRelativeCoord(cloneObj.left, cloneObj.top, cloneObj.get("angle"));

      let clipBoxLeft = clipBoxRelativeLeft;
      let clipBoxTop = clipBoxRelativeTop;

      if (lessThan(clipBoxRelativeLeft, originalObjRelativeLeft)) {
        clipBoxLeft = originalObjRelativeLeft;
      } else if (
        greaterThan(
          clipBoxRelativeLeft + scaledWidth,
          originalObjRelativeLeft + originalScaledWidth
        )
      ) {
        clipBoxLeft =
          originalObjRelativeLeft + originalScaledWidth - scaledWidth;
      }

      if (lessThan(clipBoxRelativeTop, originalObjRelativeTop)) {
        clipBoxTop = originalObjRelativeTop;
      } else if (
        greaterThan(
          clipBoxRelativeTop + scaledHeight,
          originalObjRelativeTop + originalScaledHeight
        )
      ) {
        clipBoxTop =
          originalObjRelativeTop + originalScaledHeight - scaledHeight;
      }

      [clipBoxLeft, clipBoxTop] = getRelativeCoord(
        clipBoxLeft,
        clipBoxTop,
        -clipBox.get("angle")
      );
      clipBox.set({
        left: clipBoxLeft,
        top: clipBoxTop
      });

      setClippedArea();
    });

    canvas.add(clipBox);
    clipHelperObj.clipBox = clipBox;
    canvas.setActiveObject(clipBox);

    canvas.requestRenderAll();
  }
);

// 选中
const selectedObj: Ref<FabricObject | undefined> = ref(undefined);

onBeforeSwitchPage((pageID) => {
  if (!canvas) {
    return;
  }

  const activeObject = canvas.getActiveObject();
  if (activeObject instanceof Textbox) {
    bookStore.updateElement(
      pageID,
      activeObject?.get("__ID"),
      {
        left: activeObject.left,
        top: activeObject.top,
        text: activeObject.text
      },
      true
    );
  }

  selectedObj.value = undefined;
  bookStore.setPageSelected(pageID, undefined);
});
watch(selectedObj, (newVal, oldVal, onCleanup) => {
  bookStore.setPageSelected(bookStore.currentPageID, newVal?.get("__ID"));

  if (!canvas || !newVal) {
    return;
  }

  const obj = allFabricObjects[newVal.get("__ID")];

  const onScaling = function (this: FabricObject) {
    const scaleX = getLimitedValue(
      this.scaleX,
      IMG_SCALE_MIN_VALUE,
      IMG_SCALE_MAX_VALUE
    );
    const scaleY = getLimitedValue(
      this.scaleY,
      IMG_SCALE_MIN_VALUE,
      IMG_SCALE_MAX_VALUE
    );

    this.set({
      scaleX: scaleX,
      scaleY: scaleY
    });
    this.setCoords();
    canvas?.requestRenderAll();
    bookStore.updateElement(
      bookStore.currentPageID,
      this.get("__ID"),
      {
        left: this.left,
        top: this.top,
        scaleX: this.scaleX,
        scaleY: this.scaleY
      },
      true
    );
  };
  obj.on("scaling", onScaling);

  const onModified = function (this: FabricImage | Textbox) {
    if (!canvas) {
      return;
    }

    if (this instanceof Textbox) {
      if (this.text.length < 1) {
        this.set({
          text: CANVAS_INPUT_PLACEHOLDER,
          width: TEXT_INIT_WIDTH
        });
      }

      bookStore.updateElement(
        bookStore.currentPageID,
        this.get("__ID"),
        {
          text: this.text,
          width: this.width
        },
        true
      );
    }

    limitElementInCanvas(this, canvas.width, canvas.height);
    canvas?.requestRenderAll();

    bookStore.updateElement(
      bookStore.currentPageID,
      this.get("__ID"),
      {
        left: this.left,
        top: this.top,
        width: this.width,
        scaleX: this.scaleX,
        scaleY: this.scaleY,
        angle: this.angle
      },
      true
    );
  };
  obj.on("modified", onModified);

  let isPaste: boolean | undefined = false;
  const onPaste = function () {
    isPaste = true;
  };
  const onKeyup = function () {
    isPaste = false;
  };
  const onChangeText = function (this: Textbox) {
    if (!isPaste) {
      return;
    }

    this.set("styles", {});

    while (obj.top + obj.height > bookStore.height) {
      this.set("width", obj.width + 60);
      if (obj.width > bookStore.width) {
        this.set("width", bookStore.width);
        break;
      }
    }

    canvas?.requestRenderAll();
  };

  if (obj instanceof Textbox) {
    window.addEventListener("paste", onPaste);
    window.addEventListener("keyup", onKeyup);
    obj.on("changed", onChangeText);
  }

  onCleanup(() => {
    obj.off("scaling", onScaling);
    obj.off("modified", onModified);

    window.removeEventListener("paste", onPaste);
    window.removeEventListener("keyup", onKeyup);
    (obj as Textbox).off("changed", onChangeText);
    isPaste = undefined;
  });
});

// 导出
watch(
  () => {
    return pageStore.pendingToExport;
  },
  async () => {
    if (!pageStore.pendingToExport || !canvas || bookStore.pages.length === 0) {
      return;
    }

    if (pageStore.isExporting) {
      pageStore.addTips({
        text: "正在导出中，请稍后再试"
      });
      return;
    }

    let height: number = bookStore.height;
    let generate = generatePDF;
    if ((pageStore.pendingToExport as ExportType) === ExportType.PNG) {
      height = bookStore.height * bookStore.pages.length;
      generate = generatePNG;
    }

    pageStore.setPendingToExport(undefined);
    pageStore.setIsExporting(true);

    let tipsLoadingID: string | undefined;
    try {
      tipsLoadingID = pageStore.addTips({
        type: "loading",
        text: "正在导出...",
        duration: -1,
        isOverlay: true
      });
      await generate(
        JSON.parse(JSON.stringify(bookStore.pages)),
        CANVAS_BG_COLOR,
        bookStore.width,
        height,
        "default"
      );
    } catch {
      if (tipsLoadingID) {
        pageStore.removeTips(tipsLoadingID);
      }

      tipsLoadingID = pageStore.addTips({
        type: "error",
        text: "导出失败，请稍后再试"
      });
      pageStore.setIsExporting(false);
      return;
    }

    if (tipsLoadingID) {
      pageStore.removeTips(tipsLoadingID);
    }
    tipsLoadingID = pageStore.addTips({
      text: "导出成功"
    });

    pageStore.setIsExporting(false);
    pageStore.setHasExported();
  }
);

const discardActiveObjectWhenClickNonInteractiveArea = (event: Event) => {
  const target = event.target;
  if (!target) {
    return;
  }

  if (!checkNonInteractiveArea(event.target as HTMLElement)) {
    return;
  }
  bookStore.setPageClipped(bookStore.currentPageID, undefined);
  canvas?.discardActiveObject();
  canvas?.requestRenderAll();
};
const discardActiveObjectByKey = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    canvas?.discardActiveObject();
    canvas?.requestRenderAll();
  }
};

let uploadBGBtn: FabricObject | undefined = undefined;
const setUploadBGBtn = (object: FabricObject) => {
  if (uploadBGBtn) {
    return;
  }

  uploadBGBtn = object;
};
watch(
  [
    () => {
      return bookStore.pages[bookStore.currentPageIndexInArray].bg;
    },
    () => {
      return bookStore.currentPageID;
    }
  ],
  () => {
    if (!canvas || !uploadBGBtn) {
      return;
    }

    if (bookStore.getPage(bookStore.currentPageID)?.bg) {
      canvas.remove(uploadBGBtn);
    } else {
      if (!canvas.contains(uploadBGBtn)) {
        canvas.add(uploadBGBtn);
      }
    }
    canvas.requestRenderAll();
  }
);

onMounted(() => {
  canvas = new CustomFabricCanvas("main-canvas", {
    selection: false,
    preserveObjectStacking: true
  });

  drawUploadBgImgBtn(canvas)
    .then((object: FabricObject) => {
      setUploadBGBtn(object);
    })
    .catch(() => {});
  resetCanvas();

  // 这里类型待确认
  canvas.on("mouse:dblclick", (event) => {
    const target = event.target;

    if (target instanceof FabricImage) {
      bookStore.setPageClipped(bookStore.currentPageID, target.get("__ID"));
    } else if (target instanceof Textbox) {
      target.enterEditing();
    }
  });

  canvas.on("mouse:down", (event) => {
    if (clippedObj) {
      if (!event.target || !checkClipHelperObj(event.target)) {
        bookStore.setPageClipped(bookStore.currentPageID, undefined);
      }
    }
  });
  document.addEventListener("keydown", cancelClipByKey);

  canvas.on("selection:cleared", () => {
    selectedObj.value = undefined;
  });

  const onSelection = (
    options: Partial<TEvent<TPointerEvent>> & {
      selected: FabricObject[];
    }
  ) => {
    if (
      !canvas ||
      options.selected.length !== 1 ||
      checkClipHelperObj(options.selected[0])
    ) {
      selectedObj.value = undefined;
      return;
    }

    selectedObj.value = options.selected[0];
  };
  canvas.on("selection:created", onSelection);
  canvas.on("selection:updated", onSelection);

  document.addEventListener(
    "click",
    discardActiveObjectWhenClickNonInteractiveArea
  );
  document.addEventListener("keydown", discardActiveObjectByKey);
});

onUnmounted(() => {
  if (canvas) {
    canvas.removeListeners();
    canvas.dispose();
  }
  canvas = undefined;
  document.removeEventListener("keydown", cancelClipByKey);
  document.removeEventListener(
    "click",
    discardActiveObjectWhenClickNonInteractiveArea
  );
  document.removeEventListener("keydown", discardActiveObjectByKey);
});
</script>

<template>
  <canvas id="main-canvas" :width="bookStore.width" :height="bookStore.height"
    >浏览器不支持 canvas，请更换浏览器</canvas
  >
  <image-input
    :active="imageInputActive"
    :max-quantity="imageInputQuantity"
    @update:upload="onUploadImage"
    @update:uploaded="onUploadedImage"
    multiple
  ></image-input>
</template>
