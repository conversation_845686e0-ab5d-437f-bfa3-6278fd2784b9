<!--
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-09 16:51:24
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2024-12-10 13:52:28
 * @Description:
-->
<script setup lang="ts">
import { useTemplateRef, watch } from "vue";
import { fileReader } from "@/utils/file-reader";
import { UPLOAD_IMG_MAX_SIZE } from "@/constants";
import LoadResult from "@/types/load-result-type";

const props = defineProps({
  active: Boolean, // 是否激活
  maxQuantity: {
    // 最大上传数量
    type: Number,
    default: 1
  },
  maxSize: {
    // 最大上传大小
    type: Number,
    default: UPLOAD_IMG_MAX_SIZE
  }
});

const emits = defineEmits(["update:upload", "update:uploaded"]);

const fileInputElement = useTemplateRef<HTMLInputElement>("fileInputElement");

let uploadingQuantity: number = 0;

watch(
  () => {
    return props.active;
  },
  (newIsActive) => {
    if (!newIsActive) {
      return;
    }

    if (uploadingQuantity <= 0) {
      fileInputElement.value?.click();
    } else {
      emits("update:uploaded", undefined, LoadResult.IS_LOADING);
    }
  }
);

const handleUploadedImg = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!(target.files && target.files[0])) {
    emits("update:uploaded", undefined, LoadResult.NO_FILE);
    return;
  }

  for (const f of target.files) {
    if (uploadingQuantity >= props.maxQuantity) {
      emits("update:uploaded", undefined, LoadResult.OVER_MAX_QUANTITY);
      break;
    }

    if (!f.type.startsWith("image/")) {
      emits("update:uploaded", undefined, LoadResult.NOT_IMAGE);
      continue;
    }
    if (f.size > props.maxSize) {
      emits("update:uploaded", undefined, LoadResult.OVER_MAX_SIZE);
      continue;
    }
    if (f.size === 0) {
      continue;
    }

    fileReader(f)
      .then((result) => {
        emits("update:uploaded", result);
      })
      .catch(() => {
        emits("update:uploaded", undefined, LoadResult.UNKNOWN);
      })
      .finally(() => {
        uploadingQuantity--;
        if (uploadingQuantity <= 0) {
          emits("update:uploaded", undefined, LoadResult.ALL_COMPLETE);
        }
      });
    uploadingQuantity++;
  }
  if (uploadingQuantity > 0) {
    emits("update:upload");
  }

  target.value = "";
};
</script>

<template>
  <input
    v-show="false"
    ref="fileInputElement"
    type="file"
    accept="image/jpeg,image/jpg,image/png"
    @change="handleUploadedImg"
  />
</template>
