<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-29 16:37:12
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2024-12-10 13:57:41
 * @Description:
-->
<script setup lang="ts">
import { ref, nextTick, useTemplateRef, watch, onMounted } from "vue";
import { useBookStore } from "@/stores/book";
import { usePageStore } from "@/stores/web-page";
import ImageInput from "@/components/image-input.vue";
import CanvasDrawer from "@/components/canvas-drawer.vue";
import LoadResult from "@/types/load-result-type";

const pageStore = usePageStore();
const bookStore = useBookStore();

const imageInputActive = ref(false);

const showImgInputBox = () => {
  imageInputActive.value = true;
  nextTick(() => {
    imageInputActive.value = false;
  });
};

let tipsLoadingID: string | undefined;
const onUploadImage = () => {
  tipsLoadingID = pageStore.addTips({
    type: "loading",
    text: "正在加载图片",
    duration: -1,
    isOverlay: true
  });
};

const onUploadedImage = (
  data: string | undefined,
  result?: LoadResult | undefined
) => {
  if (!data) {
    switch (result) {
      case LoadResult.IS_LOADING:
        pageStore.addTips({
          type: "warning",
          text: "已有图片正在加载中，请稍后再试"
        });
        break;
      case LoadResult.NO_FILE:
        pageStore.addTips({
          text: "未选择图片"
        });
        break;
      case LoadResult.OVER_MAX_QUANTITY:
        pageStore.addTips({
          type: "warning",
          text: "页面数量超出限制"
        });
        break;
      case LoadResult.OVER_MAX_SIZE:
        pageStore.addTips({
          type: "warning",
          text: "图片大小超出限制"
        });
        break;
      case LoadResult.NOT_IMAGE:
        pageStore.addTips({
          type: "warning",
          text: "非图片文件"
        });
        break;
      case LoadResult.ALL_COMPLETE:
        if (tipsLoadingID) {
          pageStore.removeTips(tipsLoadingID);
          tipsLoadingID = undefined;
        }
        break;
      default:
        pageStore.addTips({
          type: "error",
          text: "图片加载失败，稍后再试试"
        });
        break;
    }
    return;
  }

  // 在加载期间，如果切换了书本页，这里会设置到错误页，目前先用顶层遮罩在加载期间禁止操作，后续优化
  bookStore.addPageBG(bookStore.currentPageID, {
    src: data
  });

  pageStore.addTips({
    text: "已添加背景图"
  });
  pageStore.setHasModified();
};

const canvasDiv = useTemplateRef<HTMLDivElement>("center-main__container");
const canvas = useTemplateRef<HTMLDivElement>("center-main__container-canvas");
const translateX = ref(0);
const translateY = ref(0);
watch(
  () => {
    return pageStore.canvasScale;
  },
  () => {
    nextTick(() => {
      if (!canvasDiv.value || !canvas.value) {
        return;
      }

      const { width: outerWidth, height: outerHeight } =
        canvasDiv.value.getBoundingClientRect();
      const { width: canvasWidth, height: canvasHeight } =
        canvas.value.getBoundingClientRect();

      translateX.value =
        ((pageStore.canvasScale - 1) * (bookStore.width + 40)) /
        2 /
        pageStore.canvasScale;

      if (canvasWidth < outerWidth) {
        translateX.value = translateX.value =
          translateX.value +
          (outerWidth - canvasWidth) / 2 / pageStore.canvasScale;
      }
      translateY.value =
        ((pageStore.canvasScale - 1) * (bookStore.height + 40)) /
        2 /
        pageStore.canvasScale;
      if (canvasHeight < outerHeight) {
        translateY.value = translateY.value =
          translateY.value +
          (outerHeight - canvasHeight) / 2 / pageStore.canvasScale;
      }
    });
  },
  {
    immediate: true
  }
);

onMounted(() => {
  nextTick(() => {
    if (!canvasDiv.value || !canvas.value) {
      return;
    }

    const { width: outerWidth, height: outerHeight } =
      canvasDiv.value.getBoundingClientRect();
    const scaleX = outerWidth / (bookStore.width + 40);
    const scaleY = outerHeight / (bookStore.height + 40);

    pageStore.updateCanvasScale(
      Math.max(Math.min(Math.min(scaleX, scaleY), 1), 0.3)
    );
  });
});
</script>

<template>
  <v-main class="center-main">
    <div class="center-main__container" ref="center-main__container">
      <div
        class="center-main__container-canvas pa-5 position-relative"
        :style="{
          transform: `scale(${pageStore.canvasScale}) translate(${translateX}px, ${translateY}px)`
        }"
        ref="center-main__container-canvas"
      >
        <canvas-drawer></canvas-drawer>
        <image-input
          :active="imageInputActive"
          @update:upload="onUploadImage"
          @update:uploaded="onUploadedImage"
        ></image-input>
        <div
          class="center-main__upload-btn"
          v-show="!bookStore.getPage(bookStore.currentPageID)?.bg"
          @click="showImgInputBox"
        ></div>
      </div>
    </div>
  </v-main>
</template>

<style>
.center-main {
  height: 100%;
  background: linear-gradient(
    #222223 10%,
    rgb(var(--v-theme-background)) 10%,
    rgb(var(--v-theme-background)) 50%,
    #222223 50%,
    #222223 60%,
    rgb(var(--v-theme-background)) 60%,
    rgb(var(--v-theme-background)) 100%
  );
  background-position: 0 0;
  background-size: 20px 20px;
}

.center-main__container {
  overflow: auto;
  width: 100%;
  height: 100%;
}

.center-main__container-canvas {
  display: inline-block;
}

.center-main__upload-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 160px;
  height: 176px;
  transform: translate(-50%, -50%);
  cursor: pointer;
}
</style>
