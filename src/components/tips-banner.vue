<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-11-28 16:54:33
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2024-11-29 10:28:37
 * @Description:
-->
<script setup lang="ts">
import { onMounted } from "vue";
import type { TipsBannerType } from "@/types/tips-banner";

const { type = "info", duration = 2000 } = defineProps<{
  type?: TipsBannerType;
  duration?: number;
}>();

const show = defineModel("show", {
  type: Boolean,
  required: true
});

onMounted(() => {
  if (duration > 0) {
    setTimeout(() => {
      show.value = false;
    }, duration);
  }
});
</script>

<template>
  <div class="tips-banner" v-if="show">
    <span
      class="tips-banner__icon tips-banner__icon-loading"
      v-if="type === 'loading'"
    ></span>
    <span
      class="tips-banner__icon tips-banner__icon-warning"
      v-else-if="type === 'warning'"
    ></span>
    <span
      class="tips-banner__icon tips-banner__icon-error"
      v-else-if="type === 'error'"
    ></span>
    <div class="tips-banner__text">
      <slot></slot>
    </div>
  </div>
</template>

<style>
.tips-banner {
  display: flex;
  margin-bottom: 1rem;
  border-radius: 7px;
  padding: 0.5rem 1rem;
  max-width: 19rem;
  font-family: PingFangSC-Light, sans-serif;
  font-size: 1rem;
  font-weight: 200;
  letter-spacing: 0;
  color: #fff;
  background-color: #000;
  align-items: center;
}

@media screen and (width <= 768px) {
  .tips-banner {
    max-width: 15rem;
  }
}

.tips-banner__icon {
  margin-right: 0.5rem;
  border-radius: 50%;
  min-width: 1.375rem;
  min-height: 1.375rem;
}

.tips-banner__icon-error {
  background: url("@/assets/images/tips-error.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.tips-banner__icon-warning {
  background: url("@/assets/images/tips-warning.png");
  background-repeat: no-repeat;
  background-size: contain;
}

.tips-banner__icon-loading {
  background: url("@/assets/images/tips-loading.png");
  background-repeat: no-repeat;
  background-size: contain;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
