/*
 * @Author: ha<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2024-12-08 13:23:03
 * @Description:
 */
import { createPinia } from "pinia";

import "vuetify/styles";
import "@mdi/font/css/materialdesignicons.css";
import { createVuetify } from "vuetify";
import { darkV1 } from "@/theme";
import "@/assets/main.css";

import router from "./router";

import { createApp } from "vue";
import App from "./App.vue";

const pinna = createPinia();
const vuetify = createVuetify({
  theme: {
    defaultTheme: "dark-v1",
    themes: {
      "dark-v1": darkV1
    }
  }
});

const app = createApp(App);

app.use(pinna);
app.use(vuetify);
app.use(router);

app.mount("#app");
