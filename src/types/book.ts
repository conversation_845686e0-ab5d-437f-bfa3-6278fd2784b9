/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-23 15:25:10
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-24 16:51:36
 * @Description:
 */

export enum ElementType {
  TEXTBOX = 1,
  GRAPHICS,
  BG,
  IMAGE,
  IMG_UPLOAD_BTN
}
export enum TextDirection {
  HORIZONTAL = 1,
  VERTICAL
}

export enum AddElementResult {
  OK = 1,
  PAGE_NOT_FOUND,
  OVER_MAX_QUANTITY,
  NO_SRC,
  EXISTED
}

export enum RemoveElementResult {
  OK = 1,
  PAGE_NOT_FOUND,
  OVER_MIN_QUANTITY
}

export interface IBaseData {
  readonly ID: string;
  readonly type: ElementType;
  left: number;
  top: number;
  width: number;
  height: number;
  scaleX: number;
  scaleY: number;
  angle: number;
  opacity: number;
  depth: number;
  objHasModified: boolean;
  readonly belongToPage: number;
}

export interface ITextboxData extends IBaseData {
  text: string;
  fontSize: number;
  color: string;
  fontFamily: string;
  charSpacing: number;
  direction: TextDirection;
}

export interface IImgData extends IBaseData {
  src: string;
  shouldScaleToFitContainer: boolean;
}

export type IImgUploadBtn = IBaseData;

export interface IBookPageData {
  thumbnail: string | undefined;
  bg: IImgData | undefined;
  texts: { [key: string]: ITextboxData };
  graphics: { [key: string]: IImgData };
  images: { [key: string]: IImgData };
  imgUploadBtns: { [key: string]: IImgUploadBtn };
  selected: string | undefined;
  clipped: string | undefined;
}

export interface IBookPage extends IBookPageData {
  readonly ID: number;
}

export interface IBook {
  pages: IBookPage[];
  prePages: IBookPage[];
  currentPageIndexInArray: number;
  currentPageID: number;
  lastPageID: number;
  width: number;
  height: number;
}
