/*
 * @Author: han<PERSON><PERSON> hanlin<PERSON>@zuoyebang.com
 * @Date: 2024-10-23 14:28:19
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-28 14:41:25
 * @Description:
 */
export function fileReader(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = (error) => {
      reject(error);
    };
    reader.readAsDataURL(file);
  });
}
