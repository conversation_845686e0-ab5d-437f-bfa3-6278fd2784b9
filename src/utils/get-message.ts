/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-31 17:56:00
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-28 15:13:58
 * @Description:
 */
import {
  MESSAGE_WHEN_EXITING_PAGE_AFTER_EXPORT,
  MESSAGE_WHEN_EXITING_PAGE_BEFORE_EXPORT
} from "@/constants/index";
import { usePageStore } from "@/stores/web-page";

const pageStore = usePageStore();
export function getExitingMessage(): string {
  // 触发确认提示

  return pageStore.hasExported
    ? MESSAGE_WHEN_EXITING_PAGE_AFTER_EXPORT
    : MESSAGE_WHEN_EXITING_PAGE_BEFORE_EXPORT;
}
