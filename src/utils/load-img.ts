/*
 * @Author: ha<PERSON><PERSON><PERSON> hanlin<PERSON>@zuoyebang.com
 * @Date: 2025-05-29 17:15:58
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-05 10:03:39
 * @Description:
 */
async function loadImg(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "Anonymous";
    img.onload = () => {
      resolve(img);
    };
    img.onerror = () => {
      reject(new Error("图片加载失败"));
    };
    img.src = src;
  });
}
export default loadImg;
