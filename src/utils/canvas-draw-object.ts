/*
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-11-14 15:58:01
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-06-24 18:53:12
 * @Description:
 */
import type { IImgData, ITextboxData } from "@/types/book";
import { FabricImage, Textbox, Group, IText, Rect } from "fabric";
import { CustomFabricCanvas } from "@/custom-fabric";
import UPLOAD_BG_BTN from "@/assets/images/upload-img.png";
import { loadFontFamily } from "./handle-font-family";
import { TEXT_FONT_FAMILY } from "@/constants";

export const drawFabricImage = async (
  container: CustomFabricCanvas | Group,
  config: IImgData,
  offsetX: number = 0,
  offsetY: number = 0
) => {
  try {
    console.log(config.width, config.height);
    const oImg = await FabricImage.fromURL(config.src, undefined, {
      __ID: config.ID,
      left: config.left + offsetX,
      top: config.top + offsetY,
      width: config.width,
      height: config.height,
      scaleX: config.scaleX,
      scaleY: config.scaleY,
      angle: config.angle,
      opacity: config.opacity,
      depth: config.depth,
      lockScalingFlip: true
    });

    if (container instanceof CustomFabricCanvas) {
      if (!container.destroyed) {
        container.add(oImg);
        container.requestRenderAll();
      }
    } else {
      container.add(oImg);
    }

    return oImg;
  } catch {
    return;
  }
};

export const drawFabricTextbox = async (
  container: CustomFabricCanvas | Group,
  config: ITextboxData,
  offsetX: number = 0,
  offsetY: number = 0
) => {
  try {
    await loadFontFamily(
      config.fontFamily,
      TEXT_FONT_FAMILY[config.fontFamily as keyof typeof TEXT_FONT_FAMILY].url
    );
  } catch {}

  const obj = new Textbox(config.text, {
    __ID: config.ID,
    left: config.left + offsetX,
    top: config.top + offsetY,
    width: config.width,
    height: config.height,
    scaleX: config.scaleX,
    scaleY: config.scaleY,
    angle: config.angle,
    opacity: config.opacity,
    fontSize: config.fontSize,
    fontFamily: config.fontFamily,
    charSpacing: config.charSpacing,
    fill: config.color,
    depth: config.depth,
    lockScalingY: true,
    splitByGrapheme: true
  });
  obj.setControlsVisibility({
    mt: false,
    mb: false,
    ml: true,
    mr: true,
    bl: false,
    br: false,
    tl: false,
    tr: false
  });

  container.add(obj);
  if (container instanceof CustomFabricCanvas) {
    container.requestRenderAll();
  }
  return obj;
};

export const drawUploadBgImgBtn = (canvas: CustomFabricCanvas) => {
  return FabricImage.fromURL(UPLOAD_BG_BTN).then((img) => {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    img.set({
      left: centerX - img.width / 2,
      top: centerY - img.height / 2 - 21,
      selectable: false,
      evented: false
    });
    const rect = new Rect({
      left: centerX - 159 / 2,
      top: centerY - 125 / 2 - 21,
      width: 159,
      height: 125,
      rx: 10, // 圆角的水平半径
      ry: 10,
      fill: "transparent",
      stroke: "#BCB5B5",
      strokeWidth: 1,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false
    });

    const text = new IText("添加图片", {
      fontSize: 20,
      fill: "#BCB5B5",
      selectable: false,
      evented: false
    });
    text.set({
      left: centerX - text.width / 2,
      top: centerY - text.height / 2 + 79
    });
    const group = new Group([img, rect, text]);
    group.set({ depth: 0, selectable: false, hoverCursor: "pointer" });

    canvas.add(group);
    canvas.requestRenderAll();
    return group;
  });
};

export const scaleElementToFit = (
  oImg: FabricImage,
  width: number,
  height: number
) => {
  const scaleX = width / oImg.width;
  const scaleY = height / oImg.height;
  if (scaleX < 1 || scaleY < 1) {
    oImg.scale(scaleX < scaleY ? scaleX : scaleY);
  }
};
