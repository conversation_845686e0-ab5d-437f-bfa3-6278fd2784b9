/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-24 16:19:39
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-01-20 21:42:32
 * @Description:
 */
import { jsPDF } from "jspdf";
import { Rect } from "fabric";
import type { IBookPage } from "@/types/book";
import { drawFabricImage, drawFabricTextbox } from "./canvas-draw-object";
import { CustomGroup } from "@/custom-fabric/index";

export async function generatePDF(
  pages: IBookPage[],
  backgroundColor: string,
  width: number,
  height: number,
  name: string
) {
  let doc: jsPDF | undefined = new jsPDF({
    orientation: width > height ? "l" : "p",
    unit: "px",
    format: [width, height],
    hotfixes: ["px_scaling"]
  });

  const bgObj = new Rect({
    left: 0,
    top: 0,
    strokeWidth: 0,
    width: width,
    height: height,
    depth: 0,
    fill: backgroundColor
  });

  let group: CustomGroup | undefined = new CustomGroup([bgObj]);

  for (const page of pages) {
    if (page.bg) {
      const bgConfig = page.bg;
      await drawFabricImage(group, bgConfig);
    }
    for (const key in page.images) {
      const imageConfig = page.images[key];
      await drawFabricImage(group, imageConfig);
    }
    for (const key in page.graphics) {
      const graphicsConfig = page.graphics[key];
      await drawFabricImage(group, graphicsConfig);
    }
    for (const key in page.texts) {
      const textConfig = page.texts[key];
      drawFabricTextbox(group, textConfig);
    }

    doc.addImage(
      group.toDataURL({
        left: -group.left,
        top: -group.top,
        width: width,
        height: height
      }),
      0,
      0,
      width,
      height
    );
    doc.addPage();
    group.removeAll();
    group.add(bgObj);
  }

  group.removeAll();
  group = undefined;

  doc.deletePage(doc.getNumberOfPages());

  await doc.save(name, { returnPromise: true });

  doc = undefined;
}

export async function generatePNG(
  pages: IBookPage[],
  backgroundColor: string,
  width: number,
  height: number,
  name: string
) {
  const bgObjConfig = {
    left: 0,
    top: 0,
    strokeWidth: 0,
    width: width,
    height: height,
    depth: 0,
    fill: backgroundColor
  };

  const group = new CustomGroup([new Rect(bgObjConfig)]);

  const offsetHeight = height / pages.length;
  let i = 0;

  for (const page of pages) {
    const g = new CustomGroup([
      new Rect(bgObjConfig).set("height", offsetHeight)
    ]);
    if (page.bg) {
      const bgConfig = page.bg;
      await drawFabricImage(g, bgConfig);
    }
    for (const key in page.images) {
      const imageConfig = page.images[key];
      await drawFabricImage(g, imageConfig);
    }
    for (const key in page.graphics) {
      const graphicsConfig = page.graphics[key];
      await drawFabricImage(g, graphicsConfig);
    }
    for (const key in page.texts) {
      const textConfig = page.texts[key];
      drawFabricTextbox(g, textConfig);
    }

    g.clipPath = new Rect({
      left: -g.width / 2 - g.left,
      top: -g.height / 2 - g.top,
      width: width,
      height: offsetHeight
    });

    g.set("top", g.top + i * offsetHeight);

    group.add(g);
    i++;
  }

  const canvas = document.createElement("canvas");
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext("2d");
  const objects = group.getObjects();
  for (const obj of objects) {
    obj.render(ctx as CanvasRenderingContext2D);
  }

  const byteCharacters = atob(canvas.toDataURL("image/png", 1).split(",")[1]);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: "image/png" }); // 确保类型正确
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = name + ".png"; // 设置导出文件名
  document.body.appendChild(link);
  link.click();
  URL.revokeObjectURL(link.href);
  document.body.removeChild(link);
}
