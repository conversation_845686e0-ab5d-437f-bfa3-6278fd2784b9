/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-12 17:45:42
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-13 13:44:45
 * @Description:
 */
import WebFont from "webfontloader";
import { TEXT_FONT_FAMILY_LOAD_TIMEOUT } from "@/constants";

const FONT_FAMILY_IS_LOADED: string[] = [];

export const checkFontFamilyIsLoaded = (fontFamilyName: string) => {
  return FONT_FAMILY_IS_LOADED.includes(fontFamilyName);
};

export const loadFontFamily = (fontFamilyName: string, url: string) => {
  if (checkFontFamilyIsLoaded(fontFamilyName)) {
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    WebFont.load({
      custom: {
        families: [fontFamilyName],
        urls: [url]
      },
      timeout: TEXT_FONT_FAMILY_LOAD_TIMEOUT,
      active: () => {
        FONT_FAMILY_IS_LOADED.push(fontFamilyName);
        resolve("ok");
      },
      inactive: reject
    });
  });
};
