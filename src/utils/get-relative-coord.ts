/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-07 20:47:08
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-07 21:04:26
 * @Description:
 */
import { DEG_TO_RAD } from "@/constants";

export default function getRelativeCoord(
  x: number,
  y: number,
  angleInDegrees: number
) {
  const angleInRadians = angleInDegrees * DEG_TO_RAD;
  const cos = Math.cos(angleInRadians);
  const sin = Math.sin(angleInRadians);

  // 计算新坐标
  const newLeft = x * cos + y * sin;
  const newTop = -x * sin + y * cos;

  return [newLeft, newTop];
}
