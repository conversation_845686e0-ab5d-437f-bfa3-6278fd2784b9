/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-06 12:00:02
 * @LastEditors: hanlin<PERSON> <EMAIL>
 * @LastEditTime: 2024-11-07 17:07:41
 * @Description:
 */
import * as fabric from "fabric";
import { DEG_TO_RAD } from "@/constants";

const LINE_WIDTH = 3,
  LINE_LENGTH = 15,
  LINE_COLOR = "black";

const drawAngleLine = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  lineLength: number,
  lineWidth: number,
  strokeStyle: string,
  angle1: number,
  angle2: number
) => {
  ctx.strokeStyle = strokeStyle;
  ctx.lineWidth = lineWidth;
  // 绘制自定义控件
  ctx.beginPath();

  ctx.moveTo(x, y);
  ctx.lineTo(
    x + lineLength * Math.cos(angle1 * DEG_TO_RAD),
    y + lineLength * Math.sin(angle1 * DEG_TO_RAD)
  );
  ctx.moveTo(x, y);
  ctx.lineTo(
    x + lineLength * Math.cos(angle2 * DEG_TO_RAD),
    y + lineLength * Math.sin(angle2 * DEG_TO_RAD)
  );

  ctx.stroke();
  ctx.closePath();
};

export default function setClipControl(obj: fabric.Rect) {
  obj.controls.tl.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle,
      fabricObject.angle + 90
    );
  };
  obj.controls.tr.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle + 90,
      fabricObject.angle + 180
    );
  };
  obj.controls.bl.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle,
      fabricObject.angle + 270
    );
  };
  obj.controls.br.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle + 180,
      fabricObject.angle + 270
    );
  };
  obj.controls.mt.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle,
      fabricObject.angle + 180
    );
  };

  obj.controls.mb.render = obj.controls.mt.render;

  obj.controls.ml.render = function (
    ctx,
    left,
    top,
    styleOverride,
    fabricObject
  ) {
    let strokeStyle;
    if (styleOverride && styleOverride.cornerStrokeColor) {
      strokeStyle = styleOverride.cornerStrokeColor;
    } else {
      strokeStyle = LINE_COLOR;
    }
    drawAngleLine(
      ctx,
      left,
      top,
      LINE_LENGTH,
      LINE_WIDTH,
      strokeStyle,
      fabricObject.angle + 270,
      fabricObject.angle + 90
    );
  };
  obj.controls.mr.render = obj.controls.ml.render;
}
