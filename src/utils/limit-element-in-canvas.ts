/*
 * @Author: ha<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-12-22 16:43:12
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-22 16:47:42
 * @Description:
 */
import { Textbox, FabricImage } from "fabric";

const OFFSET = 50;

export default function limitElementInCanvas(
  element: FabricImage | Textbox,
  width: number,
  height: number
): void {
  element.setCoords();

  if (!element.isOnScreen()) {
    if (element.top < 0) {
      element.set("top", OFFSET);
    } else if (element.top > height) {
      element.set("top", height - OFFSET);
    }
    if (element.left < 0) {
      element.set("left", OFFSET);
    } else if (element.left > width) {
      element.set("left", width - OFFSET);
    }
  }
  element.setCoords();
}
